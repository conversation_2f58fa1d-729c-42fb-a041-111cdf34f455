{"sysTime":"2025-06-18 16:21:13.755","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"background-preinit","className":"o.h.validator.internal.util.Version:21","methodName":"o.h.validator.internal.util.Version:<clinit>-21","message":"HV000001: Hibernate Validator 8.0.1.Final","thrown":""}
{"sysTime":"2025-06-18 16:21:13.746","level":"WARN","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.a.nacos.client.logging.NacosLogging:64","methodName":"c.a.nacos.client.logging.NacosLogging:loadConfiguration-64","message":"{\"msg\":\"Load Logback Configuration of Nacos fail, message: org/slf4j/impl/StaticLoggerBinder\"}","thrown":""}
{"sysTime":"2025-06-18 16:21:13.793","level":"WARN","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.a.nacos.client.logging.NacosLogging:64","methodName":"c.a.nacos.client.logging.NacosLogging:loadConfiguration-64","message":"{\"msg\":\"Load Logback Configuration of Nacos fail, message: org/slf4j/impl/StaticLoggerBinder\"}","thrown":""}
{"sysTime":"2025-06-18 16:21:13.937","level":"WARN","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.a.nacos.client.logging.NacosLogging:64","methodName":"c.a.nacos.client.logging.NacosLogging:loadConfiguration-64","message":"{\"msg\":\"Load Logback Configuration of Nacos fail, message: org/slf4j/impl/StaticLoggerBinder\"}","thrown":""}
{"sysTime":"2025-06-18 16:21:13.938","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.alibaba.nacos.client.utils.ParamUtil:88","methodName":"c.alibaba.nacos.client.utils.ParamUtil:<clinit>-88","message":"[settings] [req-serv] nacos-server port:8848","thrown":""}
{"sysTime":"2025-06-18 16:21:13.938","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.alibaba.nacos.client.utils.ParamUtil:99","methodName":"c.alibaba.nacos.client.utils.ParamUtil:<clinit>-99","message":"[settings] [http-client] connect timeout:1000","thrown":""}
{"sysTime":"2025-06-18 16:21:13.938","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.alibaba.nacos.client.utils.ParamUtil:106","methodName":"c.alibaba.nacos.client.utils.ParamUtil:<clinit>-106","message":"PER_TASK_CONFIG_SIZE: 3000.0","thrown":""}
{"sysTime":"2025-06-18 16:21:13.956","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"Thread-1","className":"c.a.n.client.identify.CredentialWatcher:165","methodName":"c.a.n.client.identify.CredentialWatcher:loadCredential-165","message":"null No credential found","thrown":""}
{"sysTime":"2025-06-18 16:21:13.959","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.a.n.client.identify.CredentialWatcher:165","methodName":"c.a.n.client.identify.CredentialWatcher:loadCredential-165","message":"null No credential found","thrown":""}
{"sysTime":"2025-06-18 16:21:15.022","level":"WARN","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.a.nacos.client.logging.NacosLogging:64","methodName":"c.a.nacos.client.logging.NacosLogging:loadConfiguration-64","message":"{\"msg\":\"Load Logback Configuration of Nacos fail, message: org/slf4j/impl/StaticLoggerBinder\"}","thrown":""}
{"sysTime":"2025-06-18 16:21:15.031","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.a.n.c.c.impl.LocalConfigInfoProcessor:212","methodName":"c.a.n.c.c.impl.LocalConfigInfoProcessor:<clinit>-212","message":"LOCAL_SNAPSHOT_PATH:/Users/<USER>/nacos/config","thrown":""}
{"sysTime":"2025-06-18 16:21:15.043","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.a.nacos.client.config.impl.Limiter:54","methodName":"c.a.nacos.client.config.impl.Limiter:<clinit>-54","message":"limitTime:5.0","thrown":""}
{"sysTime":"2025-06-18 16:21:15.625","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.a.nacos.client.config.utils.JvmUtil:49","methodName":"c.a.nacos.client.config.utils.JvmUtil:<clinit>-49","message":"isMultiInstance:false","thrown":""}
{"sysTime":"2025-06-18 16:21:15.880","level":"WARN","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.a.c.n.c.NacosPropertySourceBuilder:97","methodName":"c.a.c.n.c.NacosPropertySourceBuilder:loadNacosData-97","message":"{\"msg\":\"Ignore the empty nacos configuration and get it based on dataId[delivery-application] & group[apply]\"}","thrown":""}
{"sysTime":"2025-06-18 16:21:15.945","level":"WARN","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.a.c.n.c.NacosPropertySourceBuilder:97","methodName":"c.a.c.n.c.NacosPropertySourceBuilder:loadNacosData-97","message":"{\"msg\":\"Ignore the empty nacos configuration and get it based on dataId[delivery-application-dev.yaml] & group[apply]\"}","thrown":""}
{"sysTime":"2025-06-18 16:21:15.947","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"o.s.c.b.c.PropertySourceBootstrapConfiguration:133","methodName":"o.s.c.b.c.PropertySourceBootstrapConfiguration:doInitialize-133","message":"Located property source: [BootstrapPropertySource {name='bootstrapProperties-delivery-application-dev.yaml,apply'}, BootstrapPropertySource {name='bootstrapProperties-delivery-application.yaml,apply'}, BootstrapPropertySource {name='bootstrapProperties-delivery-application,apply'}, BootstrapPropertySource {name='bootstrapProperties-apply-cos-tencent.properties,apply'}, BootstrapPropertySource {name='bootstrapProperties-jd-config.yaml,apply'}, BootstrapPropertySource {name='bootstrapProperties-kd100-config.yaml,apply'}, BootstrapPropertySource {name='bootstrapProperties-yunda-config.yaml,apply'}, BootstrapPropertySource {name='bootstrapProperties-common-starter-config.yaml,apply'}, BootstrapPropertySource {name='bootstrapProperties-delivery-config.yaml,apply'}, BootstrapPropertySource {name='bootstrapProperties-delivery-mysql.yaml,apply'}]","thrown":""}
{"sysTime":"2025-06-18 16:21:16.002","level":"WARN","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.a.nacos.client.logging.NacosLogging:64","methodName":"c.a.nacos.client.logging.NacosLogging:loadConfiguration-64","message":"{\"msg\":\"Load Logback Configuration of Nacos fail, message: org/slf4j/impl/StaticLoggerBinder\"}","thrown":""}
{"sysTime":"2025-06-18 16:21:16.008","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.e.d.application.DeliveryApplication:660","methodName":"c.e.d.application.DeliveryApplication:logStartupProfileInfo-660","message":"The following 1 profile is active: \"dev\"","thrown":""}
{"sysTime":"2025-06-18 16:21:16.792","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"o.s.d.r.c.RepositoryConfigurationDelegate:292","methodName":"o.s.d.r.c.RepositoryConfigurationDelegate:multipleStoresDetected-292","message":"Multiple Spring Data modules found, entering strict repository configuration mode","thrown":""}
{"sysTime":"2025-06-18 16:21:16.793","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"o.s.d.r.c.RepositoryConfigurationDelegate:139","methodName":"o.s.d.r.c.RepositoryConfigurationDelegate:registerRepositoriesIn-139","message":"Bootstrapping Spring Data Redis repositories in DEFAULT mode.","thrown":""}
{"sysTime":"2025-06-18 16:21:16.814","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"o.s.d.r.c.RepositoryConfigurationDelegate:208","methodName":"o.s.d.r.c.RepositoryConfigurationDelegate:registerRepositoriesIn-208","message":"Finished Spring Data repository scanning in 15 ms. Found 0 Redis repository interfaces.","thrown":""}
{"sysTime":"2025-06-18 16:21:16.997","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"o.s.cloud.context.scope.GenericScope:282","methodName":"o.s.cloud.context.scope.GenericScope:setSerializationId-282","message":"BeanFactory id=46ae1105-295c-3a07-870c-b61a124b4f7d","thrown":""}
{"sysTime":"2025-06-18 16:21:17.206","level":"WARN","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-437","message":"{\"msg\":\"Bean 'org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration' of type [org.springframework.cloud.commons.config.CommonsConfigAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [sentinelBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.\"}","thrown":""}
{"sysTime":"2025-06-18 16:21:17.208","level":"WARN","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-437","message":"{\"msg\":\"Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [sentinelBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.\"}","thrown":""}
{"sysTime":"2025-06-18 16:21:17.209","level":"WARN","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-437","message":"{\"msg\":\"Bean 'loadBalancerClientsDefaultsMappingsProvider' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerDefaultMappingsProviderAutoConfiguration$$Lambda$836/0x0000007001713cf0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [sentinelBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.\"}","thrown":""}
{"sysTime":"2025-06-18 16:21:17.213","level":"WARN","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-437","message":"{\"msg\":\"Bean 'defaultsBindHandlerAdvisor' of type [org.springframework.cloud.commons.config.DefaultsBindHandlerAdvisor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [sentinelBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.\"}","thrown":""}
{"sysTime":"2025-06-18 16:21:17.225","level":"WARN","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-437","message":"{\"msg\":\"Bean 'spring.datasource.dynamic-com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceProperties] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [sentinelBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.\"}","thrown":""}
{"sysTime":"2025-06-18 16:21:17.230","level":"WARN","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-437","message":"{\"msg\":\"Bean 'com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration' of type [com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration$$SpringCGLIB$$0] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [sentinelBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.\"}","thrown":""}
{"sysTime":"2025-06-18 16:21:17.235","level":"WARN","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-437","message":"{\"msg\":\"Bean 'dsProcessor' of type [com.baomidou.dynamic.datasource.processor.jakarta.DsJakartaHeaderProcessor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [sentinelBeanPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.\"}","thrown":""}
{"sysTime":"2025-06-18 16:21:17.254","level":"WARN","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:429","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-429","message":"{\"msg\":\"Bean 'org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig' of type [org.springframework.cloud.client.loadbalancer.LoadBalancerAutoConfiguration$DeferringLoadBalancerInterceptorConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). The currently created BeanPostProcessor [lbRestClientPostProcessor] is declared through a non-static factory method on that class; consider declaring it as static instead.\"}","thrown":""}
{"sysTime":"2025-06-18 16:21:17.256","level":"WARN","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:437","methodName":"o.s.c.s.PostProcessorRegistrationDelegate$BeanPostProcessorChecker:postProcessAfterInitialization-437","message":"{\"msg\":\"Bean 'deferringLoadBalancerInterceptor' of type [org.springframework.cloud.client.loadbalancer.DeferringLoadBalancerInterceptor] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying). Is this bean getting eagerly injected into a currently created BeanPostProcessor [lbRestClientPostProcessor]? Check the corresponding BeanPostProcessor declaration and its dependencies.\"}","thrown":""}
{"sysTime":"2025-06-18 16:21:17.396","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"o.s.b.w.embedded.tomcat.TomcatWebServer:109","methodName":"o.s.b.w.embedded.tomcat.TomcatWebServer:initialize-109","message":"Tomcat initialized with port 20130 (http)","thrown":""}
{"sysTime":"2025-06-18 16:21:17.404","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"o.apache.coyote.http11.Http11NioProtocol:173","methodName":"o.apache.coyote.http11.Http11NioProtocol:log-173","message":"Initializing ProtocolHandler [\"http-nio-20130\"]","thrown":""}
{"sysTime":"2025-06-18 16:21:17.406","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"o.apache.catalina.core.StandardService:173","methodName":"o.apache.catalina.core.StandardService:log-173","message":"Starting service [Tomcat]","thrown":""}
{"sysTime":"2025-06-18 16:21:17.406","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"org.apache.catalina.core.StandardEngine:173","methodName":"org.apache.catalina.core.StandardEngine:log-173","message":"Starting Servlet engine: [Apache Tomcat/10.1.31]","thrown":""}
{"sysTime":"2025-06-18 16:21:17.439","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"o.a.c.c.C.[Tomcat].[localhost].[/]:173","methodName":"o.a.c.c.C.[Tomcat].[localhost].[/]:log-173","message":"Initializing Spring embedded WebApplicationContext","thrown":""}
{"sysTime":"2025-06-18 16:21:17.439","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"o.s.b.w.s.c.ServletWebServerApplicationContext:296","methodName":"o.s.b.w.s.c.ServletWebServerApplicationContext:prepareWebApplicationContext-296","message":"Root WebApplicationContext: initialization completed in 1404 ms","thrown":""}
{"sysTime":"2025-06-18 16:21:19.553","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"com.alibaba.druid.pool.DruidDataSource:1002","methodName":"com.alibaba.druid.pool.DruidDataSource:init-1002","message":"{dataSource-1,db-issuer-admin} inited","thrown":""}
{"sysTime":"2025-06-18 16:21:20.955","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"com.alibaba.druid.pool.DruidDataSource:1002","methodName":"com.alibaba.druid.pool.DruidDataSource:init-1002","message":"{dataSource-2,db-issuer-admin-proxy} inited","thrown":""}
{"sysTime":"2025-06-18 16:21:22.684","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"com.alibaba.druid.pool.DruidDataSource:1002","methodName":"com.alibaba.druid.pool.DruidDataSource:init-1002","message":"{dataSource-3,db-issuer-admin-minor} inited","thrown":""}
{"sysTime":"2025-06-18 16:21:24.360","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"com.alibaba.druid.pool.DruidDataSource:1002","methodName":"com.alibaba.druid.pool.DruidDataSource:init-1002","message":"{dataSource-4,db-service} inited","thrown":""}
{"sysTime":"2025-06-18 16:21:24.362","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.b.d.d.DynamicRoutingDataSource:154","methodName":"c.b.d.d.DynamicRoutingDataSource:addDataSource-154","message":"dynamic-datasource - add a datasource named [db-issuer-admin-proxy] success","thrown":""}
{"sysTime":"2025-06-18 16:21:24.362","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.b.d.d.DynamicRoutingDataSource:154","methodName":"c.b.d.d.DynamicRoutingDataSource:addDataSource-154","message":"dynamic-datasource - add a datasource named [db-issuer-admin] success","thrown":""}
{"sysTime":"2025-06-18 16:21:24.363","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.b.d.d.DynamicRoutingDataSource:154","methodName":"c.b.d.d.DynamicRoutingDataSource:addDataSource-154","message":"dynamic-datasource - add a datasource named [db-service] success","thrown":""}
{"sysTime":"2025-06-18 16:21:24.363","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.b.d.d.DynamicRoutingDataSource:154","methodName":"c.b.d.d.DynamicRoutingDataSource:addDataSource-154","message":"dynamic-datasource - add a datasource named [db-issuer-admin-minor] success","thrown":""}
{"sysTime":"2025-06-18 16:21:24.364","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.b.d.d.DynamicRoutingDataSource:234","methodName":"c.b.d.d.DynamicRoutingDataSource:afterPropertiesSet-234","message":"dynamic-datasource initial loaded [4] datasource,primary datasource named [db-issuer-admin]","thrown":""}
{"sysTime":"2025-06-18 16:21:25.217","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:26","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-26","message":"ets-group-event-delivery defaultProducer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-06-18 16:21:25.280","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:34","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-34","message":"rocketmq producer server 开启成功----------------------------------","thrown":""}
{"sysTime":"2025-06-18 16:21:26.734","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"org.redisson.Version:43","methodName":"org.redisson.Version:logVersion-43","message":"Redisson 3.38.1","thrown":""}
{"sysTime":"2025-06-18 16:21:26.920","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"redisson-netty-2-6","className":"o.redisson.connection.ConnectionsHolder:132","methodName":"o.redisson.connection.ConnectionsHolder:lambda$initConnections$1-132","message":"1 connections initialized for ************/************:6379","thrown":""}
{"sysTime":"2025-06-18 16:21:28.464","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"redisson-netty-2-19","className":"o.redisson.connection.ConnectionsHolder:132","methodName":"o.redisson.connection.ConnectionsHolder:lambda$initConnections$1-132","message":"24 connections initialized for ************/************:6379","thrown":""}
{"sysTime":"2025-06-18 16:21:28.892","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:26","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-26","message":"ets-group-email-delivery defaultProducer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-06-18 16:21:28.895","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:34","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-34","message":"rocketmq producer server 开启成功----------------------------------","thrown":""}
{"sysTime":"2025-06-18 16:21:28.930","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"com.alibaba.nacos.client.naming:65","methodName":"com.alibaba.nacos.client.naming:call-65","message":"initializer namespace from System Property :null","thrown":""}
{"sysTime":"2025-06-18 16:21:28.930","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"com.alibaba.nacos.client.naming:74","methodName":"com.alibaba.nacos.client.naming:call-74","message":"initializer namespace from System Environment :null","thrown":""}
{"sysTime":"2025-06-18 16:21:28.930","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"com.alibaba.nacos.client.naming:84","methodName":"com.alibaba.nacos.client.naming:call-84","message":"initializer namespace from System Property :null","thrown":""}
{"sysTime":"2025-06-18 16:21:28.936","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.e.deploy.NacosDynamicServerListWatcher:37","methodName":"c.e.deploy.NacosDynamicServerListWatcher:startWatch-37","message":"启动nacos服务变更监听: ","thrown":""}
{"sysTime":"2025-06-18 16:21:29.027","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"com.alibaba.nacos.client.naming:242","methodName":"com.alibaba.nacos.client.naming:processServiceJson-242","message":"new ips(1) service: apply@@delivery-application -> [{\"instanceId\":\"**************#20130#DEFAULT#apply@@delivery-application\",\"ip\":\"**************\",\"port\":20130,\"weight\":1.0,\"healthy\":true,\"enabled\":true,\"ephemeral\":true,\"clusterName\":\"DEFAULT\",\"serviceName\":\"apply@@delivery-application\",\"metadata\":{\"preserved.register.source\":\"SPRING_CLOUD\"},\"instanceHeartBeatInterval\":5000,\"instanceHeartBeatTimeOut\":15000,\"ipDeleteTimeout\":30000}]","thrown":""}
{"sysTime":"2025-06-18 16:21:29.030","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"com.alibaba.nacos.client.naming:281","methodName":"com.alibaba.nacos.client.naming:processServiceJson-281","message":"current ips:(1) service: apply@@delivery-application -> [{\"instanceId\":\"**************#20130#DEFAULT#apply@@delivery-application\",\"ip\":\"**************\",\"port\":20130,\"weight\":1.0,\"healthy\":true,\"enabled\":true,\"ephemeral\":true,\"clusterName\":\"DEFAULT\",\"serviceName\":\"apply@@delivery-application\",\"metadata\":{\"preserved.register.source\":\"SPRING_CLOUD\"},\"instanceHeartBeatInterval\":5000,\"instanceHeartBeatTimeOut\":15000,\"ipDeleteTimeout\":30000}]","thrown":""}
{"sysTime":"2025-06-18 16:21:29.041","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:26","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-26","message":"ets_express_group defaultProducer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-06-18 16:21:29.043","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:34","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-34","message":"rocketmq producer server 开启成功----------------------------------","thrown":""}
{"sysTime":"2025-06-18 16:21:29.045","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:55","methodName":"com.ets.common.queue.QueueBaseConfig:consumerStart-55","message":"defaultConsumer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-06-18 16:21:29.219","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:82","methodName":"com.ets.common.queue.QueueBaseConfig:consumerStart-82","message":"consumer 创建成功 groupName=ets_express_group, nameServerAddr=name-service:9876, topic=ets_express_topic, tag=queueExpress","thrown":""}
{"sysTime":"2025-06-18 16:21:29.229","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:26","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-26","message":"ets_review_group defaultProducer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-06-18 16:21:29.232","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:34","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-34","message":"rocketmq producer server 开启成功----------------------------------","thrown":""}
{"sysTime":"2025-06-18 16:21:29.234","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:55","methodName":"com.ets.common.queue.QueueBaseConfig:consumerStart-55","message":"defaultConsumer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-06-18 16:21:29.392","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:82","methodName":"com.ets.common.queue.QueueBaseConfig:consumerStart-82","message":"consumer 创建成功 groupName=ets_review_group, nameServerAddr=name-service:9876, topic=ets_review_topic, tag=queueReview","thrown":""}
{"sysTime":"2025-06-18 16:21:29.398","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:26","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-26","message":"ets_java_delivery_group defaultProducer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-06-18 16:21:29.401","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:34","methodName":"com.ets.common.queue.QueueBaseConfig:createProducer-34","message":"rocketmq producer server 开启成功----------------------------------","thrown":""}
{"sysTime":"2025-06-18 16:21:29.404","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:55","methodName":"com.ets.common.queue.QueueBaseConfig:consumerStart-55","message":"defaultConsumer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-06-18 16:21:29.582","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:82","methodName":"com.ets.common.queue.QueueBaseConfig:consumerStart-82","message":"consumer 创建成功 groupName=ets_java_delivery_group, nameServerAddr=name-service:9876, topic=ets_java_delivery_task_topic, tag=queueTask","thrown":""}
{"sysTime":"2025-06-18 16:21:29.723","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:55","methodName":"com.ets.common.queue.QueueBaseConfig:consumerStart-55","message":"defaultConsumer 正在创建---------------------------------------","thrown":""}
{"sysTime":"2025-06-18 16:21:29.862","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"com.ets.common.queue.QueueBaseConfig:82","methodName":"com.ets.common.queue.QueueBaseConfig:consumerStart-82","message":"consumer 创建成功 groupName=ets-group-event-delivery, nameServerAddr=name-service:9876, topic=ETS_EVENT, tag=queueEvent","thrown":""}
{"sysTime":"2025-06-18 16:21:29.941","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent","className":"c.e.deploy.NacosDynamicServerListWatcher:42","methodName":"c.e.deploy.NacosDynamicServerListWatcher:lambda$startWatch$0-42","message":"监听到nacos服务变更：serviceName: apply@@delivery-application, groupName：apply, 实例：[Instance{instanceId='**************#20130#DEFAULT#apply@@delivery-application', ip='**************', port=20130, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='apply@@delivery-application', metadata={preserved.register.source=SPRING_CLOUD}}]","thrown":""}
{"sysTime":"2025-06-18 16:21:30.536","level":"WARN","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger:94","methodName":"o.s.c.l.c.LoadBalancerCacheAutoConfiguration$LoadBalancerCaffeineWarnLogger:afterPropertiesSet-94","message":"{\"msg\":\"Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.\"}","thrown":""}
{"sysTime":"2025-06-18 16:21:30.546","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"o.s.b.a.e.web.EndpointLinksResolver:58","methodName":"o.s.b.a.e.web.EndpointLinksResolver:<init>-58","message":"Exposing 21 endpoint(s) beneath base path '/actuator'","thrown":""}
{"sysTime":"2025-06-18 16:21:30.649","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"o.apache.coyote.http11.Http11NioProtocol:173","methodName":"o.apache.coyote.http11.Http11NioProtocol:log-173","message":"Starting ProtocolHandler [\"http-nio-20130\"]","thrown":""}
{"sysTime":"2025-06-18 16:21:30.654","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"o.s.b.w.embedded.tomcat.TomcatWebServer:241","methodName":"o.s.b.w.embedded.tomcat.TomcatWebServer:start-241","message":"Tomcat started on port 20130 (http) with context path ''","thrown":""}
{"sysTime":"2025-06-18 16:21:30.655","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"com.alibaba.nacos.client.naming:81","methodName":"com.alibaba.nacos.client.naming:addBeatInfo-81","message":"[BEAT] adding beat: BeatInfo{port=20130, ip='************', weight=1.0, serviceName='apply@@delivery-application', cluster='DEFAULT', metadata={IPv6=null, preserved.register.source=SPRING_CLOUD}, scheduled=false, period=5000, stopped=false} to beat map.","thrown":""}
{"sysTime":"2025-06-18 16:21:30.655","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"com.alibaba.nacos.client.naming:238","methodName":"com.alibaba.nacos.client.naming:registerService-238","message":"[REGISTER-SERVICE] ets-dev registering service apply@@delivery-application with instance: Instance{instanceId='null', ip='************', port=20130, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={IPv6=null, preserved.register.source=SPRING_CLOUD}}","thrown":""}
{"sysTime":"2025-06-18 16:21:30.687","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.a.c.n.registry.NacosServiceRegistry:76","methodName":"c.a.c.n.registry.NacosServiceRegistry:register-76","message":"nacos registry, apply delivery-application ************:20130 register finished","thrown":""}
{"sysTime":"2025-06-18 16:21:30.728","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.e.d.application.DeliveryApplication:56","methodName":"c.e.d.application.DeliveryApplication:logStarted-56","message":"Started DeliveryApplication in 18.377 seconds (process running for 18.84)","thrown":""}
{"sysTime":"2025-06-18 16:21:30.731","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:169","methodName":"c.a.n.client.config.impl.ClientWorker:addCacheDataIfAbsent-169","message":"[fixed-nacos.public_8848-ets-dev] [subscribe] common-starter-config.yaml+apply+ets-dev","thrown":""}
{"sysTime":"2025-06-18 16:21:30.731","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.a.nacos.client.config.impl.CacheData:92","methodName":"c.a.nacos.client.config.impl.CacheData:addListener-92","message":"[fixed-nacos.public_8848-ets-dev] [add-listener] ok, tenant=ets-dev, dataId=common-starter-config.yaml, group=apply, cnt=1","thrown":""}
{"sysTime":"2025-06-18 16:21:30.731","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.a.c.n.refresh.NacosContextRefresher:131","methodName":"c.a.c.n.refresh.NacosContextRefresher:registerNacosListener-131","message":"[Nacos Config] Listening config: dataId=common-starter-config.yaml, group=apply","thrown":""}
{"sysTime":"2025-06-18 16:21:30.731","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:169","methodName":"c.a.n.client.config.impl.ClientWorker:addCacheDataIfAbsent-169","message":"[fixed-nacos.public_8848-ets-dev] [subscribe] delivery-application-dev.yaml+apply+ets-dev","thrown":""}
{"sysTime":"2025-06-18 16:21:30.731","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.a.nacos.client.config.impl.CacheData:92","methodName":"c.a.nacos.client.config.impl.CacheData:addListener-92","message":"[fixed-nacos.public_8848-ets-dev] [add-listener] ok, tenant=ets-dev, dataId=delivery-application-dev.yaml, group=apply, cnt=1","thrown":""}
{"sysTime":"2025-06-18 16:21:30.731","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.a.c.n.refresh.NacosContextRefresher:131","methodName":"c.a.c.n.refresh.NacosContextRefresher:registerNacosListener-131","message":"[Nacos Config] Listening config: dataId=delivery-application-dev.yaml, group=apply","thrown":""}
{"sysTime":"2025-06-18 16:21:30.732","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:169","methodName":"c.a.n.client.config.impl.ClientWorker:addCacheDataIfAbsent-169","message":"[fixed-nacos.public_8848-ets-dev] [subscribe] jd-config.yaml+apply+ets-dev","thrown":""}
{"sysTime":"2025-06-18 16:21:30.732","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.a.nacos.client.config.impl.CacheData:92","methodName":"c.a.nacos.client.config.impl.CacheData:addListener-92","message":"[fixed-nacos.public_8848-ets-dev] [add-listener] ok, tenant=ets-dev, dataId=jd-config.yaml, group=apply, cnt=1","thrown":""}
{"sysTime":"2025-06-18 16:21:30.732","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.a.c.n.refresh.NacosContextRefresher:131","methodName":"c.a.c.n.refresh.NacosContextRefresher:registerNacosListener-131","message":"[Nacos Config] Listening config: dataId=jd-config.yaml, group=apply","thrown":""}
{"sysTime":"2025-06-18 16:21:30.732","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:169","methodName":"c.a.n.client.config.impl.ClientWorker:addCacheDataIfAbsent-169","message":"[fixed-nacos.public_8848-ets-dev] [subscribe] delivery-application+apply+ets-dev","thrown":""}
{"sysTime":"2025-06-18 16:21:30.732","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.a.nacos.client.config.impl.CacheData:92","methodName":"c.a.nacos.client.config.impl.CacheData:addListener-92","message":"[fixed-nacos.public_8848-ets-dev] [add-listener] ok, tenant=ets-dev, dataId=delivery-application, group=apply, cnt=1","thrown":""}
{"sysTime":"2025-06-18 16:21:30.732","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.a.c.n.refresh.NacosContextRefresher:131","methodName":"c.a.c.n.refresh.NacosContextRefresher:registerNacosListener-131","message":"[Nacos Config] Listening config: dataId=delivery-application, group=apply","thrown":""}
{"sysTime":"2025-06-18 16:21:30.732","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:169","methodName":"c.a.n.client.config.impl.ClientWorker:addCacheDataIfAbsent-169","message":"[fixed-nacos.public_8848-ets-dev] [subscribe] delivery-config.yaml+apply+ets-dev","thrown":""}
{"sysTime":"2025-06-18 16:21:30.732","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.a.nacos.client.config.impl.CacheData:92","methodName":"c.a.nacos.client.config.impl.CacheData:addListener-92","message":"[fixed-nacos.public_8848-ets-dev] [add-listener] ok, tenant=ets-dev, dataId=delivery-config.yaml, group=apply, cnt=1","thrown":""}
{"sysTime":"2025-06-18 16:21:30.732","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.a.c.n.refresh.NacosContextRefresher:131","methodName":"c.a.c.n.refresh.NacosContextRefresher:registerNacosListener-131","message":"[Nacos Config] Listening config: dataId=delivery-config.yaml, group=apply","thrown":""}
{"sysTime":"2025-06-18 16:21:30.732","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:169","methodName":"c.a.n.client.config.impl.ClientWorker:addCacheDataIfAbsent-169","message":"[fixed-nacos.public_8848-ets-dev] [subscribe] yunda-config.yaml+apply+ets-dev","thrown":""}
{"sysTime":"2025-06-18 16:21:30.732","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.a.nacos.client.config.impl.CacheData:92","methodName":"c.a.nacos.client.config.impl.CacheData:addListener-92","message":"[fixed-nacos.public_8848-ets-dev] [add-listener] ok, tenant=ets-dev, dataId=yunda-config.yaml, group=apply, cnt=1","thrown":""}
{"sysTime":"2025-06-18 16:21:30.732","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.a.c.n.refresh.NacosContextRefresher:131","methodName":"c.a.c.n.refresh.NacosContextRefresher:registerNacosListener-131","message":"[Nacos Config] Listening config: dataId=yunda-config.yaml, group=apply","thrown":""}
{"sysTime":"2025-06-18 16:21:30.733","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:169","methodName":"c.a.n.client.config.impl.ClientWorker:addCacheDataIfAbsent-169","message":"[fixed-nacos.public_8848-ets-dev] [subscribe] kd100-config.yaml+apply+ets-dev","thrown":""}
{"sysTime":"2025-06-18 16:21:30.733","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.a.nacos.client.config.impl.CacheData:92","methodName":"c.a.nacos.client.config.impl.CacheData:addListener-92","message":"[fixed-nacos.public_8848-ets-dev] [add-listener] ok, tenant=ets-dev, dataId=kd100-config.yaml, group=apply, cnt=1","thrown":""}
{"sysTime":"2025-06-18 16:21:30.733","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.a.c.n.refresh.NacosContextRefresher:131","methodName":"c.a.c.n.refresh.NacosContextRefresher:registerNacosListener-131","message":"[Nacos Config] Listening config: dataId=kd100-config.yaml, group=apply","thrown":""}
{"sysTime":"2025-06-18 16:21:30.733","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:169","methodName":"c.a.n.client.config.impl.ClientWorker:addCacheDataIfAbsent-169","message":"[fixed-nacos.public_8848-ets-dev] [subscribe] delivery-application.yaml+apply+ets-dev","thrown":""}
{"sysTime":"2025-06-18 16:21:30.733","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.a.nacos.client.config.impl.CacheData:92","methodName":"c.a.nacos.client.config.impl.CacheData:addListener-92","message":"[fixed-nacos.public_8848-ets-dev] [add-listener] ok, tenant=ets-dev, dataId=delivery-application.yaml, group=apply, cnt=1","thrown":""}
{"sysTime":"2025-06-18 16:21:30.733","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.a.c.n.refresh.NacosContextRefresher:131","methodName":"c.a.c.n.refresh.NacosContextRefresher:registerNacosListener-131","message":"[Nacos Config] Listening config: dataId=delivery-application.yaml, group=apply","thrown":""}
{"sysTime":"2025-06-18 16:21:30.733","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.a.n.client.config.impl.ClientWorker:169","methodName":"c.a.n.client.config.impl.ClientWorker:addCacheDataIfAbsent-169","message":"[fixed-nacos.public_8848-ets-dev] [subscribe] delivery-mysql.yaml+apply+ets-dev","thrown":""}
{"sysTime":"2025-06-18 16:21:30.733","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.a.nacos.client.config.impl.CacheData:92","methodName":"c.a.nacos.client.config.impl.CacheData:addListener-92","message":"[fixed-nacos.public_8848-ets-dev] [add-listener] ok, tenant=ets-dev, dataId=delivery-mysql.yaml, group=apply, cnt=1","thrown":""}
{"sysTime":"2025-06-18 16:21:30.733","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.a.c.n.refresh.NacosContextRefresher:131","methodName":"c.a.c.n.refresh.NacosContextRefresher:registerNacosListener-131","message":"[Nacos Config] Listening config: dataId=delivery-mysql.yaml, group=apply","thrown":""}
{"sysTime":"2025-06-18 16:21:30.739","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:retryAfterSalesReviewsNotifyHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4691b83d[class com.ets.delivery.application.app.job.AfterSalesReviewsNotifyJob$$SpringCGLIB$$0#retryAfterSalesReviewsNotifyHandler]","thrown":""}
{"sysTime":"2025-06-18 16:21:30.739","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:manualNotifyAfterSalesReviewHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@3b847d31[class com.ets.delivery.application.app.job.AfterSalesReviewsNotifyJob$$SpringCGLIB$$0#manualNotifyAfterSalesReviewHandler]","thrown":""}
{"sysTime":"2025-06-18 16:21:30.739","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:erpOrderDailyCheckHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@672e3f24[class com.ets.delivery.application.app.job.ErpJob$$SpringCGLIB$$0#erpOrderDailyCheckHandler]","thrown":""}
{"sysTime":"2025-06-18 16:21:30.739","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:fixErpOrderHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@11084cb5[class com.ets.delivery.application.app.job.ErpJob$$SpringCGLIB$$0#fixErpOrderHandler]","thrown":""}
{"sysTime":"2025-06-18 16:21:30.739","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:expressSubscribe, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@5a934ef9[class com.ets.delivery.application.app.job.ExpressJob$$SpringCGLIB$$0#expressSubscribe]","thrown":""}
{"sysTime":"2025-06-18 16:21:30.739","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:yundaLogisticsExpressQueryHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@42e6cd6f[class com.ets.delivery.application.app.job.LogisticsQueryJob$$SpringCGLIB$$0#yundaLogisticsExpressQueryHandler]","thrown":""}
{"sysTime":"2025-06-18 16:21:30.739","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:fixLogisticsExpressStatusHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@54271d08[class com.ets.delivery.application.app.job.LogisticsQueryJob$$SpringCGLIB$$0#fixLogisticsExpressStatusHandler]","thrown":""}
{"sysTime":"2025-06-18 16:21:30.740","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:LogisticsQueryHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@7198edd[class com.ets.delivery.application.app.job.LogisticsQueryJob$$SpringCGLIB$$0#logisticsQueryHandler]","thrown":""}
{"sysTime":"2025-06-18 16:21:30.740","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:pickUpQueryTraceInfoHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@47bdfc41[class com.ets.delivery.application.app.job.PickUpJob$$SpringCGLIB$$0#pickUpQueryTraceInfoHandler]","thrown":""}
{"sysTime":"2025-06-18 16:21:30.740","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:pickUpAutoCancelHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@33c4dc6c[class com.ets.delivery.application.app.job.PickUpJob$$SpringCGLIB$$0#pickUpAutoCancelHandler]","thrown":""}
{"sysTime":"2025-06-18 16:21:30.740","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:postReviewReleaseHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@5fe74841[class com.ets.delivery.application.app.job.PostReviewJob$$SpringCGLIB$$0#postReviewReleaseHandler]","thrown":""}
{"sysTime":"2025-06-18 16:21:30.740","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:initPostReviewDataHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@14c35a06[class com.ets.delivery.application.app.job.PostReviewJob$$SpringCGLIB$$0#initPostReviewDataHandler]","thrown":""}
{"sysTime":"2025-06-18 16:21:30.740","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:reviewDateSummaryHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@3cb4e32b[class com.ets.delivery.application.app.job.PostReviewSummaryJob$$SpringCGLIB$$0#reviewDateSummaryHandler]","thrown":""}
{"sysTime":"2025-06-18 16:21:30.740","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:reviewUserSummaryHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@29866f6d[class com.ets.delivery.application.app.job.PostReviewSummaryJob$$SpringCGLIB$$0#reviewUserSummaryHandler]","thrown":""}
{"sysTime":"2025-06-18 16:21:30.740","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:checkOvertimeHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@4cc69f5c[class com.ets.delivery.application.app.job.SendBackJob$$SpringCGLIB$$0#checkOvertimeHandler]","thrown":""}
{"sysTime":"2025-06-18 16:21:30.740","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:logisticsAvgHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@425befa8[class com.ets.delivery.application.app.job.StockAlarmJob$$SpringCGLIB$$0#logisticsAvgHandler]","thrown":""}
{"sysTime":"2025-06-18 16:21:30.740","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:goodsStockHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@59ede173[class com.ets.delivery.application.app.job.StockAlarmJob$$SpringCGLIB$$0#goodsStockHandler]","thrown":""}
{"sysTime":"2025-06-18 16:21:30.740","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:storageAlarmFileHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@438a0024[class com.ets.delivery.application.app.job.StockAlarmJob$$SpringCGLIB$$0#storageAlarmFileHandler]","thrown":""}
{"sysTime":"2025-06-18 16:21:30.740","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:stockOutSkuHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@3bce4b76[class com.ets.delivery.application.app.job.StockAlarmJob$$SpringCGLIB$$0#stockOutSkuHandler]","thrown":""}
{"sysTime":"2025-06-18 16:21:30.740","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:storageMapAddressCheckHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@26f5c508[class com.ets.delivery.application.app.job.StorageMapJob$$SpringCGLIB$$0#storageMa1pAddressCheck]","thrown":""}
{"sysTime":"2025-06-18 16:21:30.741","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.xxl.job.core.executor.XxlJobExecutor:211","methodName":"c.xxl.job.core.executor.XxlJobExecutor:registJobHandler-211","message":">>>>>>>>>>> xxl-job register jobhandler success, name:reExecHandler, jobHandler:com.xxl.job.core.handler.impl.MethodJobHandler@10a55edc[class com.ets.delivery.application.app.job.TaskJob$$SpringCGLIB$$0#reExecHandler]","thrown":""}
{"sysTime":"2025-06-18 16:21:30.866","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"RMI TCP Connection(16)-************","className":"o.a.c.c.C.[Tomcat].[localhost].[/]:173","methodName":"o.a.c.c.C.[Tomcat].[localhost].[/]:log-173","message":"Initializing Spring DispatcherServlet 'dispatcherServlet'","thrown":""}
{"sysTime":"2025-06-18 16:21:30.866","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"RMI TCP Connection(16)-************","className":"o.s.web.servlet.DispatcherServlet:532","methodName":"o.s.web.servlet.DispatcherServlet:initServletBean-532","message":"Initializing Servlet 'dispatcherServlet'","thrown":""}
{"sysTime":"2025-06-18 16:21:30.868","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"RMI TCP Connection(16)-************","className":"o.s.web.servlet.DispatcherServlet:554","methodName":"o.s.web.servlet.DispatcherServlet:initServletBean-554","message":"Completed initialization in 2 ms","thrown":""}
{"sysTime":"2025-06-18 16:21:30.952","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"o.s.c.openfeign.FeignClientFactoryBean:468","methodName":"o.s.c.openfeign.FeignClientFactoryBean:getTarget-468","message":"For 'base-application' URL not provided. Will try picking an instance via load-balancing.","thrown":""}
{"sysTime":"2025-06-18 16:21:31.090","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"main","className":"c.x.r.r.provider.XxlRpcProviderFactory:197","methodName":"c.x.r.r.provider.XxlRpcProviderFactory:addService-197","message":">>>>>>>>>>> xxl-rpc, provider factory add service success. serviceKey = com.xxl.job.core.biz.ExecutorBiz, serviceBean = class com.xxl.job.core.biz.impl.ExecutorBizImpl","thrown":""}
{"sysTime":"2025-06-18 16:21:31.098","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"Thread-33","className":"com.xxl.rpc.remoting.net.Server:66","methodName":"com.xxl.rpc.remoting.net.Server:run-66","message":">>>>>>>>>>> xxl-rpc remoting server start success, nettype = com.xxl.rpc.remoting.net.impl.netty_http.server.NettyHttpServer, port = 20132","thrown":""}
{"sysTime":"2025-06-18 16:21:31.901","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"com.alibaba.nacos.naming.push.receiver","className":"com.alibaba.nacos.client.naming:97","methodName":"com.alibaba.nacos.client.naming:run-97","message":"received push data: {\"type\":\"dom\",\"data\":\"{\\\"hosts\\\":[{\\\"ip\\\":\\\"**************\\\",\\\"port\\\":20130,\\\"valid\\\":true,\\\"healthy\\\":true,\\\"marked\\\":false,\\\"instanceId\\\":\\\"**************#20130#DEFAULT#apply@@delivery-application\\\",\\\"metadata\\\":{\\\"preserved.register.source\\\":\\\"SPRING_CLOUD\\\"},\\\"enabled\\\":true,\\\"weight\\\":1.0,\\\"clusterName\\\":\\\"DEFAULT\\\",\\\"serviceName\\\":\\\"apply@@delivery-application\\\",\\\"ephemeral\\\":true},{\\\"ip\\\":\\\"************\\\",\\\"port\\\":20130,\\\"valid\\\":true,\\\"healthy\\\":true,\\\"marked\\\":false,\\\"instanceId\\\":\\\"************#20130#DEFAULT#apply@@delivery-application\\\",\\\"metadata\\\":{\\\"preserved.register.source\\\":\\\"SPRING_CLOUD\\\"},\\\"enabled\\\":true,\\\"weight\\\":1.0,\\\"clusterName\\\":\\\"DEFAULT\\\",\\\"serviceName\\\":\\\"apply@@delivery-application\\\",\\\"ephemeral\\\":true}],\\\"dom\\\":\\\"apply@@delivery-application\\\",\\\"name\\\":\\\"apply@@delivery-application\\\",\\\"cacheMillis\\\":10000,\\\"lastRefTime\\\":1750234891905,\\\"checksum\\\":\\\"7d34b48e16aaea2a7263002221c22d12\\\",\\\"useSpecifiedURL\\\":false,\\\"clusters\\\":\\\"\\\",\\\"env\\\":\\\"\\\",\\\"metadata\\\":{}}\",\"lastRefTime\":34300162001641502} from /**********","thrown":""}
{"sysTime":"2025-06-18 16:21:31.902","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"com.alibaba.nacos.naming.push.receiver","className":"com.alibaba.nacos.client.naming:242","methodName":"com.alibaba.nacos.client.naming:processServiceJson-242","message":"new ips(1) service: apply@@delivery-application -> [{\"instanceId\":\"************#20130#DEFAULT#apply@@delivery-application\",\"ip\":\"************\",\"port\":20130,\"weight\":1.0,\"healthy\":true,\"enabled\":true,\"ephemeral\":true,\"clusterName\":\"DEFAULT\",\"serviceName\":\"apply@@delivery-application\",\"metadata\":{\"preserved.register.source\":\"SPRING_CLOUD\"},\"instanceHeartBeatInterval\":5000,\"instanceHeartBeatTimeOut\":15000,\"ipDeleteTimeout\":30000}]","thrown":""}
{"sysTime":"2025-06-18 16:21:31.902","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"com.alibaba.nacos.naming.push.receiver","className":"com.alibaba.nacos.client.naming:281","methodName":"com.alibaba.nacos.client.naming:processServiceJson-281","message":"current ips:(2) service: apply@@delivery-application -> [{\"instanceId\":\"**************#20130#DEFAULT#apply@@delivery-application\",\"ip\":\"**************\",\"port\":20130,\"weight\":1.0,\"healthy\":true,\"enabled\":true,\"ephemeral\":true,\"clusterName\":\"DEFAULT\",\"serviceName\":\"apply@@delivery-application\",\"metadata\":{\"preserved.register.source\":\"SPRING_CLOUD\"},\"instanceHeartBeatInterval\":5000,\"instanceHeartBeatTimeOut\":15000,\"ipDeleteTimeout\":30000},{\"instanceId\":\"************#20130#DEFAULT#apply@@delivery-application\",\"ip\":\"************\",\"port\":20130,\"weight\":1.0,\"healthy\":true,\"enabled\":true,\"ephemeral\":true,\"clusterName\":\"DEFAULT\",\"serviceName\":\"apply@@delivery-application\",\"metadata\":{\"preserved.register.source\":\"SPRING_CLOUD\"},\"instanceHeartBeatInterval\":5000,\"instanceHeartBeatTimeOut\":15000,\"ipDeleteTimeout\":30000}]","thrown":""}
{"sysTime":"2025-06-18 16:21:31.902","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent","className":"c.e.deploy.NacosDynamicServerListWatcher:42","methodName":"c.e.deploy.NacosDynamicServerListWatcher:lambda$startWatch$0-42","message":"监听到nacos服务变更：serviceName: apply@@delivery-application, groupName：apply, 实例：[Instance{instanceId='**************#20130#DEFAULT#apply@@delivery-application', ip='**************', port=20130, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='apply@@delivery-application', metadata={preserved.register.source=SPRING_CLOUD}}, Instance{instanceId='************#20130#DEFAULT#apply@@delivery-application', ip='************', port=20130, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='apply@@delivery-application', metadata={preserved.register.source=SPRING_CLOUD}}]","thrown":""}
{"sysTime":"2025-06-18 16:21:31.903","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"com.alibaba.nacos.naming.push.receiver","className":"com.alibaba.nacos.client.naming:97","methodName":"com.alibaba.nacos.client.naming:run-97","message":"received push data: {\"type\":\"dom\",\"data\":\"{\\\"hosts\\\":[{\\\"ip\\\":\\\"**************\\\",\\\"port\\\":20130,\\\"valid\\\":true,\\\"healthy\\\":true,\\\"marked\\\":false,\\\"instanceId\\\":\\\"**************#20130#DEFAULT#apply@@delivery-application\\\",\\\"metadata\\\":{\\\"preserved.register.source\\\":\\\"SPRING_CLOUD\\\"},\\\"enabled\\\":true,\\\"weight\\\":1.0,\\\"clusterName\\\":\\\"DEFAULT\\\",\\\"serviceName\\\":\\\"apply@@delivery-application\\\",\\\"ephemeral\\\":true},{\\\"ip\\\":\\\"************\\\",\\\"port\\\":20130,\\\"valid\\\":true,\\\"healthy\\\":true,\\\"marked\\\":false,\\\"instanceId\\\":\\\"************#20130#DEFAULT#apply@@delivery-application\\\",\\\"metadata\\\":{\\\"preserved.register.source\\\":\\\"SPRING_CLOUD\\\"},\\\"enabled\\\":true,\\\"weight\\\":1.0,\\\"clusterName\\\":\\\"DEFAULT\\\",\\\"serviceName\\\":\\\"apply@@delivery-application\\\",\\\"ephemeral\\\":true}],\\\"dom\\\":\\\"apply@@delivery-application\\\",\\\"name\\\":\\\"apply@@delivery-application\\\",\\\"cacheMillis\\\":10000,\\\"lastRefTime\\\":1750234891905,\\\"checksum\\\":\\\"7d34b48e16aaea2a7263002221c22d12\\\",\\\"useSpecifiedURL\\\":false,\\\"clusters\\\":\\\"\\\",\\\"env\\\":\\\"\\\",\\\"metadata\\\":{}}\",\"lastRefTime\":34300243083963925} from /**********","thrown":""}
{"sysTime":"2025-06-18 16:21:46.491","level":"INFO","ip":"************","appName":"delivery-application","traceId":"e12bf9d7-cc95-40c6-874d-be719cb97d39","spanId":"0","parentId":"","exportable":"","pid":"18603","thread":"http-nio-20130-exec-1","className":"c.e.s.interceptor.BeforeInterceptor:61","methodName":"c.e.s.interceptor.BeforeInterceptor:preHandle-61","message":"{\"header\":{\"content-length\":\"1364\",\"host\":\"127.0.0.1:20130\",\"content-type\":\"application/json\",\"connection\":\"keep-alive\",\"accept-encoding\":\"gzip, deflate, br\",\"user-agent\":\"Apifox/1.0.0 (https://apifox.com)\",\"accept\":\"*/*\"},\"requestUri\":\"/aftersales-reviews/create\",\"params\":{},\"postData\":\"{\\t\\\"orderSn\\\": \\\"SH1250618160000018\\\",\\t\\\"orderType\\\": \\\"reactivate\\\",\\t\\\"issuerId\\\": 1,\\t\\\"uid\\\": 1991827199,\\t\\\"plateNo\\\": \\\"苏ZXXPPV\\\",\\t\\\"plateColor\\\": 0,\\t\\\"reviewVehicleInfo\\\": {\\t\\t\\\"plateNo\\\": \\\"鲁Q5ZN78\\\",\\t\\t\\\"plateColor\\\": null,\\t\\t\\\"owner\\\": \\\"李武山\\\",\\t\\t\\\"type\\\": \\\"小型轿车\\\",\\t\\t\\\"useCharacter\\\": \\\"非营运\\\",\\t\\t\\\"passengers\\\": \\\"5人\\\",\\t\\t\\\"frontImgUrl\\\": \\\"http://dev-cos.etczs.net/common/20250618/d30dc44416dca774e07769b725ae0ea1\\\",\\t\\t\\\"backImgUrl\\\": \\\"http://dev-cos.etczs.net/common/20250618/830e4ff7e9fefb86eb04f05b20f0a0b2\\\",\\t\\t\\\"gearActivateImgUrl\\\": \\\"http://dev-cos.etczs.net/common/20250618/8c7bbf156aff1f5bf82173f1605e7203\\\",\\t\\t\\\"frontCarImgUrl\\\": \\\"http://dev-cos.etczs.net/common/20250618/1eb895e473401b230fc75f4ab1896dd4\\\"\\t},\\t\\\"orderVehicleInfo\\\": {\\t\\t\\\"plateNo\\\": \\\"苏ZXXPPV\\\",\\t\\t\\\"plateColor\\\": 0,\\t\\t\\\"owner\\\": \\\"赖国城\\\",\\t\\t\\\"type\\\": \\\"小型轿车\\\",\\t\\t\\\"useCharacter\\\": \\\"非营运\\\",\\t\\t\\\"passengers\\\": \\\"5人\\\",\\t\\t\\\"frontImgUrl\\\": \\\"http://dev-cos.etczs.net/vehicle_front/20250528/36f04584cb889fe2ab51b547aa03529f\\\",\\t\\t\\\"backImgUrl\\\": \\\"http://dev-cos.etczs.net/vehicle_back/20250528/0efaa51ed5ec6ba0e773fb90311c501b\\\",\\t\\t\\\"gearActivateImgUrl\\\": \\\"http://dev-cos.etczs.net/gear_img/20250528/e5267aea561c83a28e237037c0591f2d\\\",\\t\\t\\\"frontCarImgUrl\\\": \\\"http://dev-cos.etczs.net/car_header_img/20250528/6b4c4e6bfdb43d9048beb99b62e4cf18\\\"\\t},\\t\\\"notifyUrl\\\": \\\"http://etc-saleafter-main:6610/reactivate/reviewResult\\\"}\"}","thrown":""}
{"sysTime":"2025-06-18 16:24:19.175","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"com.alibaba.nacos.naming.beat.sender","className":"com.alibaba.nacos.client.naming:238","methodName":"com.alibaba.nacos.client.naming:registerService-238","message":"[REGISTER-SERVICE] ets-dev registering service apply@@delivery-application with instance: Instance{instanceId='null', ip='************', port=20130, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='apply@@delivery-application', metadata={IPv6=null, preserved.register.source=SPRING_CLOUD}}","thrown":""}
{"sysTime":"2025-06-18 16:24:19.193","level":"WARN","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"Thread-2","className":"c.a.n.common.http.HttpClientBeanHolder:108","methodName":"c.a.n.common.http.HttpClientBeanHolder:shutdown-108","message":"{\"msg\":\"[HttpClientBeanHolder] Start destroying common HttpClient\"}","thrown":""}
{"sysTime":"2025-06-18 16:24:19.193","level":"WARN","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"Thread-17","className":"c.a.nacos.common.notify.NotifyCenter:145","methodName":"c.a.nacos.common.notify.NotifyCenter:shutdown-145","message":"{\"msg\":\"[NotifyCenter] Start destroying Publisher\"}","thrown":""}
{"sysTime":"2025-06-18 16:24:19.194","level":"WARN","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"Thread-17","className":"c.a.nacos.common.notify.NotifyCenter:162","methodName":"c.a.nacos.common.notify.NotifyCenter:shutdown-162","message":"{\"msg\":\"[NotifyCenter] Destruction of the end\"}","thrown":""}
{"sysTime":"2025-06-18 16:24:19.194","level":"WARN","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"Thread-2","className":"c.a.n.common.http.HttpClientBeanHolder:114","methodName":"c.a.n.common.http.HttpClientBeanHolder:shutdown-114","message":"{\"msg\":\"[HttpClientBeanHolder] Destruction of the end\"}","thrown":""}
{"sysTime":"2025-06-18 16:24:19.317","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"com.alibaba.nacos.client.naming.updater","className":"com.alibaba.nacos.client.naming:248","methodName":"com.alibaba.nacos.client.naming:processServiceJson-248","message":"removed ips(1) service: apply@@delivery-application -> [{\"instanceId\":\"************#20130#DEFAULT#apply@@delivery-application\",\"ip\":\"************\",\"port\":20130,\"weight\":1.0,\"healthy\":true,\"enabled\":true,\"ephemeral\":true,\"clusterName\":\"DEFAULT\",\"serviceName\":\"apply@@delivery-application\",\"metadata\":{\"preserved.register.source\":\"SPRING_CLOUD\"},\"instanceHeartBeatInterval\":5000,\"instanceHeartBeatTimeOut\":15000,\"ipDeleteTimeout\":30000}]","thrown":""}
{"sysTime":"2025-06-18 16:24:19.318","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"nacos.publisher-com.alibaba.nacos.client.naming.event.InstancesChangeEvent","className":"c.e.deploy.NacosDynamicServerListWatcher:42","methodName":"c.e.deploy.NacosDynamicServerListWatcher:lambda$startWatch$0-42","message":"监听到nacos服务变更：serviceName: apply@@delivery-application, groupName：apply, 实例：[Instance{instanceId='**************#20130#DEFAULT#apply@@delivery-application', ip='**************', port=20130, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='apply@@delivery-application', metadata={preserved.register.source=SPRING_CLOUD}}]","thrown":""}
{"sysTime":"2025-06-18 16:24:19.319","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"com.alibaba.nacos.client.naming.updater","className":"com.alibaba.nacos.client.naming:281","methodName":"com.alibaba.nacos.client.naming:processServiceJson-281","message":"current ips:(1) service: apply@@delivery-application -> [{\"instanceId\":\"**************#20130#DEFAULT#apply@@delivery-application\",\"ip\":\"**************\",\"port\":20130,\"weight\":1.0,\"healthy\":true,\"enabled\":true,\"ephemeral\":true,\"clusterName\":\"DEFAULT\",\"serviceName\":\"apply@@delivery-application\",\"metadata\":{\"preserved.register.source\":\"SPRING_CLOUD\"},\"instanceHeartBeatInterval\":5000,\"instanceHeartBeatTimeOut\":15000,\"ipDeleteTimeout\":30000}]","thrown":""}
{"sysTime":"2025-06-18 16:24:19.351","level":"ERROR","ip":"************","appName":"delivery-application","traceId":"e12bf9d7-cc95-40c6-874d-be719cb97d39","spanId":"0","parentId":"","exportable":"","pid":"18603","thread":"http-nio-20130-exec-1","className":"c.e.s.interceptor.GlobalExceptionHandler:138","methodName":"c.e.s.interceptor.GlobalExceptionHandler:systemExceptionHandler-138","message":"{\"userId\":\"\",\"url\":\"/aftersales-reviews/create\",\"msg\":\"系统错误：null\\r\\n#org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:97)\\r\\n#org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439)\\r\\n#jdk.proxy2/jdk.proxy2.$Proxy133.insert(Unknown Source)\\r\\n#org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:272)\\r\\n#com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)\\r\\n#com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:152)\\r\\n#com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)\\r\\n#jdk.proxy2/jdk.proxy2.$Proxy233.insert(Unknown Source)\\r\\n#java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\\r\\n#java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)\\r\\n#java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\\r\\n#java.base/java.lang.reflect.Method.invoke(Method.java:569)\\r\\n#org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:351)\\r\\n#org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)\\r\\n#org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)\\r\\n#org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)\\r\\n#org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)\\r\\n#org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:220)\\r\\n#jdk.proxy2/jdk.proxy2.$Proxy234.insert(Unknown Source)\\r\\n#com.ets.common.base.MicroBaseService.create(MicroBaseService.java:161)\\r\\n#java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\\r\\n#java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)\\r\\n#java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\\r\\n#java.base/java.lang.reflect.Method.invoke(Method.java:569)\\r\\n#org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:351)\\r\\n#org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)\\r\\n#org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)\\r\\n#org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)\\r\\n#com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationInterceptor.invoke(DynamicDataSourceAnnotationInterceptor.java:50)\\r\\n#org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)\\r\\n#org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)\\r\\n#org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)\\r\\n#com.ets.delivery.application.infra.service.AftersalesReviewsService$$SpringCGLIB$$0.create(<generated>)\\r\\n#com.ets.delivery.application.app.business.aftersalesreviews.AfterSalesReviewsBusiness.createAfterSalesReview(AfterSalesReviewsBusiness.java:130)\\r\\n#com.ets.delivery.application.controller.nologin.AfterSalesReviewsController.createAfterSalesReview(AfterSalesReviewsController.java:35)\\r\\n#java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\\r\\n#java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)\\r\\n#java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\\r\\n#java.base/java.lang.reflect.Method.invoke(Method.java:569)\\r\\n#org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)\\r\\n#org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)\\r\\n#org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)\\r\\n#org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)\\r\\n#org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)\\r\\n#org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)\\r\\n#org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)\\r\\n#org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)\\r\\n#org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)\\r\\n#org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)\\r\\n#jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)\\r\\n#org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)\\r\\n#jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)\\r\\n#org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)\\r\\n#org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)\\r\\n#org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)\\r\\n#org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)\\r\\n#org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)\\r\\n#com.ets.starter.filter.RepeatedlyReadFilter.doFilter(RepeatedlyReadFilter.java:52)\\r\\n#org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)\\r\\n#org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)\\r\\n#com.ets.starter.filter.HttpServletResponseFilter.doFilter(HttpServletResponseFilter.java:37)\\r\\n#org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)\\r\\n#org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)\\r\\n#org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:113)\\r\\n#org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)\\r\\n#org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)\\r\\n#org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)\\r\\n#org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)\\r\\n#org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)\\r\\n#org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)\\r\\n#org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)\\r\\n#org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)\\r\\n#org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)\\r\\n#org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)\\r\\n#org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)\\r\\n#org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)\\r\\n#org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)\\r\\n#org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)\\r\\n#org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:384)\\r\\n#org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)\\r\\n#org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)\\r\\n#org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1741)\\r\\n#org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)\\r\\n#org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)\\r\\n#org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)\\r\\n#org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)\\r\\n#java.base/java.lang.Thread.run(Thread.java:840)\"}","thrown":""}
{"sysTime":"2025-06-18 16:24:19.787","level":"INFO","ip":"************","appName":"delivery-application","traceId":"e12bf9d7-cc95-40c6-874d-be719cb97d39","spanId":"0","parentId":"","exportable":"","pid":"18603","thread":"http-nio-20130-exec-1","className":"com.ets.common.queue.MicroBaseQueue:54","methodName":"com.ets.common.queue.MicroBaseQueue:pushRocketMqJob-54","message":"队列推送：{\"attempts\":1,\"beanName\":\"EmailJobBean\",\"className\":\"com.ets.starter.disposer.EmailDisposer\",\"isLog\":true,\"logPercentage\":100,\"params\":{\"content\":\"null\\r\\n#org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:97)\\r\\n#org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439)\\r\\n#jdk.proxy2/jdk.proxy2.$Proxy133.insert(Unknown Source)\\r\\n#org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:272)\\r\\n#com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)\\r\\n#com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:152)\\r\\n#com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)\\r\\n#jdk.proxy2/jdk.proxy2.$Proxy233.insert(Unknown Source)\\r\\n#java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\\r\\n#java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)\\r\\n#java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\\r\\n#java.base/java.lang.reflect.Method.invoke(Method.java:569)\\r\\n#org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:351)\\r\\n#org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)\\r\\n#org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)\\r\\n#org.springframework.dao.support.PersistenceExceptionTranslationInterceptor.invoke(PersistenceExceptionTranslationInterceptor.java:137)\\r\\n#org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)\\r\\n#org.springframework.aop.framework.JdkDynamicAopProxy.invoke(JdkDynamicAopProxy.java:220)\\r\\n#jdk.proxy2/jdk.proxy2.$Proxy234.insert(Unknown Source)\\r\\n#com.ets.common.base.MicroBaseService.create(MicroBaseService.java:161)\\r\\n#java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\\r\\n#java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)\\r\\n#java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\\r\\n#java.base/java.lang.reflect.Method.invoke(Method.java:569)\\r\\n#org.springframework.aop.support.AopUtils.invokeJoinpointUsingReflection(AopUtils.java:351)\\r\\n#org.springframework.aop.framework.ReflectiveMethodInvocation.invokeJoinpoint(ReflectiveMethodInvocation.java:196)\\r\\n#org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:163)\\r\\n#org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)\\r\\n#com.baomidou.dynamic.datasource.aop.DynamicDataSourceAnnotationInterceptor.invoke(DynamicDataSourceAnnotationInterceptor.java:50)\\r\\n#org.springframework.aop.framework.ReflectiveMethodInvocation.proceed(ReflectiveMethodInvocation.java:184)\\r\\n#org.springframework.aop.framework.CglibAopProxy$CglibMethodInvocation.proceed(CglibAopProxy.java:765)\\r\\n#org.springframework.aop.framework.CglibAopProxy$DynamicAdvisedInterceptor.intercept(CglibAopProxy.java:717)\\r\\n#com.ets.delivery.application.infra.service.AftersalesReviewsService$$SpringCGLIB$$0.create(<generated>)\\r\\n#com.ets.delivery.application.app.business.aftersalesreviews.AfterSalesReviewsBusiness.createAfterSalesReview(AfterSalesReviewsBusiness.java:130)\\r\\n#com.ets.delivery.application.controller.nologin.AfterSalesReviewsController.createAfterSalesReview(AfterSalesReviewsController.java:35)\\r\\n#java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\\r\\n#java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)\\r\\n#java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)\\r\\n#java.base/java.lang.reflect.Method.invoke(Method.java:569)\\r\\n#org.springframework.web.method.support.InvocableHandlerMethod.doInvoke(InvocableHandlerMethod.java:255)\\r\\n#org.springframework.web.method.support.InvocableHandlerMethod.invokeForRequest(InvocableHandlerMethod.java:188)\\r\\n#org.springframework.web.servlet.mvc.method.annotation.ServletInvocableHandlerMethod.invokeAndHandle(ServletInvocableHandlerMethod.java:118)\\r\\n#org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.invokeHandlerMethod(RequestMappingHandlerAdapter.java:926)\\r\\n#org.springframework.web.servlet.mvc.method.annotation.RequestMappingHandlerAdapter.handleInternal(RequestMappingHandlerAdapter.java:831)\\r\\n#org.springframework.web.servlet.mvc.method.AbstractHandlerMethodAdapter.handle(AbstractHandlerMethodAdapter.java:87)\\r\\n#org.springframework.web.servlet.DispatcherServlet.doDispatch(DispatcherServlet.java:1089)\\r\\n#org.springframework.web.servlet.DispatcherServlet.doService(DispatcherServlet.java:979)\\r\\n#org.springframework.web.servlet.FrameworkServlet.processRequest(FrameworkServlet.java:1014)\\r\\n#org.springframework.web.servlet.FrameworkServlet.doPost(FrameworkServlet.java:914)\\r\\n#jakarta.servlet.http.HttpServlet.service(HttpServlet.java:590)\\r\\n#org.springframework.web.servlet.FrameworkServlet.service(FrameworkServlet.java:885)\\r\\n#jakarta.servlet.http.HttpServlet.service(HttpServlet.java:658)\\r\\n#org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:195)\\r\\n#org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)\\r\\n#org.apache.tomcat.websocket.server.WsFilter.doFilter(WsFilter.java:51)\\r\\n#org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)\\r\\n#org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)\\r\\n#com.ets.starter.filter.RepeatedlyReadFilter.doFilter(RepeatedlyReadFilter.java:52)\\r\\n#org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)\\r\\n#org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)\\r\\n#com.ets.starter.filter.HttpServletResponseFilter.doFilter(HttpServletResponseFilter.java:37)\\r\\n#org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)\\r\\n#org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)\\r\\n#org.springframework.web.filter.ServerHttpObservationFilter.doFilterInternal(ServerHttpObservationFilter.java:113)\\r\\n#org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)\\r\\n#org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)\\r\\n#org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)\\r\\n#org.springframework.web.filter.CharacterEncodingFilter.doFilterInternal(CharacterEncodingFilter.java:201)\\r\\n#org.springframework.web.filter.OncePerRequestFilter.doFilter(OncePerRequestFilter.java:116)\\r\\n#org.apache.catalina.core.ApplicationFilterChain.internalDoFilter(ApplicationFilterChain.java:164)\\r\\n#org.apache.catalina.core.ApplicationFilterChain.doFilter(ApplicationFilterChain.java:140)\\r\\n#org.apache.catalina.core.StandardWrapperValve.invoke(StandardWrapperValve.java:167)\\r\\n#org.apache.catalina.core.StandardContextValve.invoke(StandardContextValve.java:90)\\r\\n#org.apache.catalina.authenticator.AuthenticatorBase.invoke(AuthenticatorBase.java:483)\\r\\n#org.apache.catalina.core.StandardHostValve.invoke(StandardHostValve.java:115)\\r\\n#org.apache.catalina.valves.ErrorReportValve.invoke(ErrorReportValve.java:93)\\r\\n#org.apache.catalina.core.StandardEngineValve.invoke(StandardEngineValve.java:74)\\r\\n#org.apache.catalina.connector.CoyoteAdapter.service(CoyoteAdapter.java:344)\\r\\n#org.apache.coyote.http11.Http11Processor.service(Http11Processor.java:384)\\r\\n#org.apache.coyote.AbstractProcessorLight.process(AbstractProcessorLight.java:63)\\r\\n#org.apache.coyote.AbstractProtocol$ConnectionHandler.process(AbstractProtocol.java:905)\\r\\n#org.apache.tomcat.util.net.NioEndpoint$SocketProcessor.doRun(NioEndpoint.java:1741)\\r\\n#org.apache.tomcat.util.net.SocketProcessorBase.run(SocketProcessorBase.java:52)\\r\\n#org.apache.tomcat.util.threads.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1190)\\r\\n#org.apache.tomcat.util.threads.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:659)\\r\\n#org.apache.tomcat.util.threads.TaskThread$WrappingRunnable.run(TaskThread.java:63)\\r\\n#java.base/java.lang.Thread.run(Thread.java:840)\\r\\n#requestInfo: {\\\"header\\\":{\\\"content-length\\\":\\\"1364\\\",\\\"host\\\":\\\"127.0.0.1:20130\\\",\\\"content-type\\\":\\\"application/json\\\",\\\"connection\\\":\\\"keep-alive\\\",\\\"accept-encoding\\\":\\\"gzip, deflate, br\\\",\\\"user-agent\\\":\\\"Apifox/1.0.0 (https://apifox.com)\\\",\\\"accept\\\":\\\"*/*\\\"},\\\"requestUri\\\":\\\"/aftersales-reviews/create\\\",\\\"params\\\":{},\\\"postData\\\":\\\"{\\\\t\\\\\\\"orderSn\\\\\\\": \\\\\\\"SH1250618160000018\\\\\\\",\\\\t\\\\\\\"orderType\\\\\\\": \\\\\\\"reactivate\\\\\\\",\\\\t\\\\\\\"issuerId\\\\\\\": 1,\\\\t\\\\\\\"uid\\\\\\\": 1991827199,\\\\t\\\\\\\"plateNo\\\\\\\": \\\\\\\"苏ZXXPPV\\\\\\\",\\\\t\\\\\\\"plateColor\\\\\\\": 0,\\\\t\\\\\\\"reviewVehicleInfo\\\\\\\": {\\\\t\\\\t\\\\\\\"plateNo\\\\\\\": \\\\\\\"鲁Q5ZN78\\\\\\\",\\\\t\\\\t\\\\\\\"plateColor\\\\\\\": null,\\\\t\\\\t\\\\\\\"owner\\\\\\\": \\\\\\\"李武山\\\\\\\",\\\\t\\\\t\\\\\\\"type\\\\\\\": \\\\\\\"小型轿车\\\\\\\",\\\\t\\\\t\\\\\\\"useCharacter\\\\\\\": \\\\\\\"非营运\\\\\\\",\\\\t\\\\t\\\\\\\"passengers\\\\\\\": \\\\\\\"5人\\\\\\\",\\\\t\\\\t\\\\\\\"frontImgUrl\\\\\\\": \\\\\\\"http://dev-cos.etczs.net/common/20250618/d30dc44416dca774e07769b725ae0ea1\\\\\\\",\\\\t\\\\t\\\\\\\"backImgUrl\\\\\\\": \\\\\\\"http://dev-cos.etczs.net/common/20250618/830e4ff7e9fefb86eb04f05b20f0a0b2\\\\\\\",\\\\t\\\\t\\\\\\\"gearActivateImgUrl\\\\\\\": \\\\\\\"http://dev-cos.etczs.net/common/20250618/8c7bbf156aff1f5bf82173f1605e7203\\\\\\\",\\\\t\\\\t\\\\\\\"frontCarImgUrl\\\\\\\": \\\\\\\"http://dev-cos.etczs.net/common/20250618/1eb895e473401b230fc75f4ab1896dd4\\\\\\\"\\\\t},\\\\t\\\\\\\"orderVehicleInfo\\\\\\\": {\\\\t\\\\t\\\\\\\"plateNo\\\\\\\": \\\\\\\"苏ZXXPPV\\\\\\\",\\\\t\\\\t\\\\\\\"plateColor\\\\\\\": 0,\\\\t\\\\t\\\\\\\"owner\\\\\\\": \\\\\\\"赖国城\\\\\\\",\\\\t\\\\t\\\\\\\"type\\\\\\\": \\\\\\\"小型轿车\\\\\\\",\\\\t\\\\t\\\\\\\"useCharacter\\\\\\\": \\\\\\\"非营运\\\\\\\",\\\\t\\\\t\\\\\\\"passengers\\\\\\\": \\\\\\\"5人\\\\\\\",\\\\t\\\\t\\\\\\\"frontImgUrl\\\\\\\": \\\\\\\"http://dev-cos.etczs.net/vehicle_front/20250528/36f04584cb889fe2ab51b547aa03529f\\\\\\\",\\\\t\\\\t\\\\\\\"backImgUrl\\\\\\\": \\\\\\\"http://dev-cos.etczs.net/vehicle_back/20250528/0efaa51ed5ec6ba0e773fb90311c501b\\\\\\\",\\\\t\\\\t\\\\\\\"gearActivateImgUrl\\\\\\\": \\\\\\\"http://dev-cos.etczs.net/gear_img/20250528/e5267aea561c83a28e237037c0591f2d\\\\\\\",\\\\t\\\\t\\\\\\\"frontCarImgUrl\\\\\\\": \\\\\\\"http://dev-cos.etczs.net/car_header_img/20250528/6b4c4e6bfdb43d9048beb99b62e4cf18\\\\\\\"\\\\t},\\\\t\\\\\\\"notifyUrl\\\\\\\": \\\\\\\"http://etc-saleafter-main:6610/reactivate/reviewResult\\\\\\\"}\\\"}\",\"title\":\"系统错误：null\"},\"retry\":0,\"spanId\":\"0.1\",\"traceId\":\"e12bf9d7-cc95-40c6-874d-be719cb97d39\",\"ttr\":300,\"useCustomRetryMode\":false}","thrown":""}
{"sysTime":"2025-06-18 16:24:19.880","level":"INFO","ip":"************","appName":"delivery-application","traceId":"e12bf9d7-cc95-40c6-874d-be719cb97d39","spanId":"0","parentId":"","exportable":"","pid":"18603","thread":"http-nio-20130-exec-1","className":"c.e.s.filter.HttpServletResponseFilter:44","methodName":"c.e.s.filter.HttpServletResponseFilter:doFilter-44","message":"{\"code\":-2,\"msg\":\"null\\r\\n#org.mybatis.spring.MyBatisExceptionTranslator.translateExceptionIfPossible(MyBatisExceptionTranslator.java:97)\\r\\n#org.mybatis.spring.SqlSessionTemplate$SqlSessionInterceptor.invoke(SqlSessionTemplate.java:439)\\r\\n#jdk.proxy2/jdk.proxy2.$Proxy133.insert(Unknown Source)\\r\\n#org.mybatis.spring.SqlSessionTemplate.insert(SqlSessionTemplate.java:272)\\r\\n#com.baomidou.mybatisplus.core.override.MybatisMapperMethod.execute(MybatisMapperMethod.java:59)\\r\\n#com.baomidou.mybatisplus.core.override.MybatisMapperProxy$PlainMethodInvoker.invoke(MybatisMapperProxy.java:152)\\r\\n#com.baomidou.mybatisplus.core.override.MybatisMapperProxy.invoke(MybatisMapperProxy.java:89)\\r\\n#jdk.proxy2/jdk.proxy2.$Proxy233.insert(Unknown Source)\\r\\n#java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)\\r\\n#java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)\\r\\n#java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.inv...","thrown":""}
{"sysTime":"2025-06-18 16:24:19.923","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"SpringApplicationShutdownHook","className":"c.a.c.n.registry.NacosServiceRegistry:95","methodName":"c.a.c.n.registry.NacosServiceRegistry:deregister-95","message":"De-registering from Nacos Server now...","thrown":""}
{"sysTime":"2025-06-18 16:24:19.924","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:100","methodName":"com.alibaba.nacos.client.naming:removeBeatInfo-100","message":"[BEAT] removing beat: apply@@delivery-application:************:20130 from beat map.","thrown":""}
{"sysTime":"2025-06-18 16:24:19.924","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:268","methodName":"com.alibaba.nacos.client.naming:deregisterService-268","message":"[DEREGISTER-SERVICE] ets-dev deregistering service apply@@delivery-application with instance: Instance{instanceId='null', ip='************', port=20130, weight=1.0, healthy=true, enabled=true, ephemeral=true, clusterName='DEFAULT', serviceName='null', metadata={}}","thrown":""}
{"sysTime":"2025-06-18 16:24:19.957","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"SpringApplicationShutdownHook","className":"c.a.c.n.registry.NacosServiceRegistry:115","methodName":"c.a.c.n.registry.NacosServiceRegistry:deregister-115","message":"De-registration finished.","thrown":""}
{"sysTime":"2025-06-18 16:24:19.960","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:146","methodName":"com.alibaba.nacos.client.naming:shutdown-146","message":"com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown begin","thrown":""}
{"sysTime":"2025-06-18 16:24:20.507","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"com.alibaba.nacos.naming.push.receiver","className":"com.alibaba.nacos.client.naming:97","methodName":"com.alibaba.nacos.client.naming:run-97","message":"received push data: {\"type\":\"dom\",\"data\":\"{\\\"hosts\\\":[{\\\"ip\\\":\\\"**************\\\",\\\"port\\\":20130,\\\"valid\\\":true,\\\"healthy\\\":true,\\\"marked\\\":false,\\\"instanceId\\\":\\\"**************#20130#DEFAULT#apply@@delivery-application\\\",\\\"metadata\\\":{\\\"preserved.register.source\\\":\\\"SPRING_CLOUD\\\"},\\\"enabled\\\":true,\\\"weight\\\":1.0,\\\"clusterName\\\":\\\"DEFAULT\\\",\\\"serviceName\\\":\\\"apply@@delivery-application\\\",\\\"ephemeral\\\":true}],\\\"dom\\\":\\\"apply@@delivery-application\\\",\\\"name\\\":\\\"apply@@delivery-application\\\",\\\"cacheMillis\\\":10000,\\\"lastRefTime\\\":1750235060521,\\\"checksum\\\":\\\"********************************\\\",\\\"useSpecifiedURL\\\":false,\\\"clusters\\\":\\\"\\\",\\\"env\\\":\\\"\\\",\\\"metadata\\\":{}}\",\"lastRefTime\":34300330618157695} from /**********","thrown":""}
{"sysTime":"2025-06-18 16:24:22.974","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:148","methodName":"com.alibaba.nacos.client.naming:shutdown-148","message":"com.alibaba.nacos.client.naming.beat.BeatReactor do shutdown stop","thrown":""}
{"sysTime":"2025-06-18 16:24:22.975","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:423","methodName":"com.alibaba.nacos.client.naming:shutdown-423","message":"com.alibaba.nacos.client.naming.core.HostReactor do shutdown begin","thrown":""}
{"sysTime":"2025-06-18 16:24:25.988","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:132","methodName":"com.alibaba.nacos.client.naming:shutdown-132","message":"com.alibaba.nacos.client.naming.core.PushReceiver do shutdown begin","thrown":""}
{"sysTime":"2025-06-18 16:24:29.005","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:136","methodName":"com.alibaba.nacos.client.naming:shutdown-136","message":"com.alibaba.nacos.client.naming.core.PushReceiver do shutdown stop","thrown":""}
{"sysTime":"2025-06-18 16:24:29.007","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:132","methodName":"com.alibaba.nacos.client.naming:shutdown-132","message":"com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown begin","thrown":""}
{"sysTime":"2025-06-18 16:24:29.007","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:134","methodName":"com.alibaba.nacos.client.naming:shutdown-134","message":"com.alibaba.nacos.client.naming.backups.FailoverReactor do shutdown stop","thrown":""}
{"sysTime":"2025-06-18 16:24:29.008","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:428","methodName":"com.alibaba.nacos.client.naming:shutdown-428","message":"com.alibaba.nacos.client.naming.core.HostReactor do shutdown stop","thrown":""}
{"sysTime":"2025-06-18 16:24:29.008","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:783","methodName":"com.alibaba.nacos.client.naming:shutdown-783","message":"com.alibaba.nacos.client.naming.net.NamingProxy do shutdown begin","thrown":""}
{"sysTime":"2025-06-18 16:24:29.009","level":"WARN","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:72","methodName":"com.alibaba.nacos.client.naming:shutdown-72","message":"{\"msg\":\"[NamingHttpClientManager] Start destroying NacosRestTemplate\"}","thrown":""}
{"sysTime":"2025-06-18 16:24:29.009","level":"WARN","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:79","methodName":"com.alibaba.nacos.client.naming:shutdown-79","message":"{\"msg\":\"[NamingHttpClientManager] Destruction of the end\"}","thrown":""}
{"sysTime":"2025-06-18 16:24:29.009","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"SpringApplicationShutdownHook","className":"c.a.n.client.identify.CredentialWatcher:105","methodName":"c.a.n.client.identify.CredentialWatcher:stop-105","message":"[null] CredentialWatcher is stopped","thrown":""}
{"sysTime":"2025-06-18 16:24:29.010","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"SpringApplicationShutdownHook","className":"c.a.n.client.identify.CredentialService:98","methodName":"c.a.n.client.identify.CredentialService:free-98","message":"[null] CredentialService is freed","thrown":""}
{"sysTime":"2025-06-18 16:24:29.010","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"SpringApplicationShutdownHook","className":"com.alibaba.nacos.client.naming:787","methodName":"com.alibaba.nacos.client.naming:shutdown-787","message":"com.alibaba.nacos.client.naming.net.NamingProxy do shutdown stop","thrown":""}
{"sysTime":"2025-06-18 16:24:30.504","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"Thread-33","className":"com.xxl.rpc.remoting.net.Server:74","methodName":"com.xxl.rpc.remoting.net.Server:run-74","message":">>>>>>>>>>> xxl-rpc remoting server stop.","thrown":""}
{"sysTime":"2025-06-18 16:24:30.590","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"xxl-job, executor ExecutorRegistryThread","className":"c.x.j.core.thread.ExecutorRegistryThread:87","methodName":"c.x.j.core.thread.ExecutorRegistryThread:run-87","message":">>>>>>>>>>> xxl-job registry-remove success, registryParam:RegistryParam{registryGroup='EXECUTOR', registryKey='etc-java-delivery-executor-dev', registryValue='************:20132'}, registryResult:ReturnT [code=200, msg=null, content=null]","thrown":""}
{"sysTime":"2025-06-18 16:24:30.592","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"xxl-job, executor ExecutorRegistryThread","className":"c.x.j.core.thread.ExecutorRegistryThread:105","methodName":"c.x.j.core.thread.ExecutorRegistryThread:run-105","message":">>>>>>>>>>> xxl-job, executor registry thread destory.","thrown":""}
{"sysTime":"2025-06-18 16:24:30.597","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"SpringApplicationShutdownHook","className":"com.xxl.rpc.remoting.net.Server:110","methodName":"com.xxl.rpc.remoting.net.Server:stop-110","message":">>>>>>>>>>> xxl-rpc remoting server destroy success.","thrown":""}
{"sysTime":"2025-06-18 16:24:30.598","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"xxl-job, executor TriggerCallbackThread","className":"c.x.j.core.thread.TriggerCallbackThread:96","methodName":"c.x.j.core.thread.TriggerCallbackThread:run-96","message":">>>>>>>>>>> xxl-job, executor callback thread destory.","thrown":""}
{"sysTime":"2025-06-18 16:24:30.598","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"Thread-32","className":"c.x.j.core.thread.TriggerCallbackThread:126","methodName":"c.x.j.core.thread.TriggerCallbackThread:run-126","message":">>>>>>>>>>> xxl-job, executor retry callback thread destory.","thrown":""}
{"sysTime":"2025-06-18 16:24:30.799","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"SpringApplicationShutdownHook","className":"c.b.d.d.DynamicRoutingDataSource:211","methodName":"c.b.d.d.DynamicRoutingDataSource:destroy-211","message":"dynamic-datasource start closing ....","thrown":""}
{"sysTime":"2025-06-18 16:24:30.803","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2204","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2204","message":"{dataSource-2} closing ...","thrown":""}
{"sysTime":"2025-06-18 16:24:30.807","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2277","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2277","message":"{dataSource-2} closed","thrown":""}
{"sysTime":"2025-06-18 16:24:30.807","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2204","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2204","message":"{dataSource-1} closing ...","thrown":""}
{"sysTime":"2025-06-18 16:24:30.808","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2277","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2277","message":"{dataSource-1} closed","thrown":""}
{"sysTime":"2025-06-18 16:24:30.808","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2204","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2204","message":"{dataSource-4} closing ...","thrown":""}
{"sysTime":"2025-06-18 16:24:30.808","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2277","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2277","message":"{dataSource-4} closed","thrown":""}
{"sysTime":"2025-06-18 16:24:30.808","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2204","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2204","message":"{dataSource-3} closing ...","thrown":""}
{"sysTime":"2025-06-18 16:24:30.808","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"SpringApplicationShutdownHook","className":"com.alibaba.druid.pool.DruidDataSource:2277","methodName":"com.alibaba.druid.pool.DruidDataSource:close-2277","message":"{dataSource-3} closed","thrown":""}
{"sysTime":"2025-06-18 16:24:30.808","level":"INFO","ip":"************","appName":"delivery-application","traceId":"","spanId":"","parentId":"","exportable":"","pid":"18603","thread":"SpringApplicationShutdownHook","className":"c.b.d.d.DynamicRoutingDataSource:215","methodName":"c.b.d.d.DynamicRoutingDataSource:destroy-215","message":"dynamic-datasource all closed success,bye","thrown":""}
