package com.ets.delivery.application.infra.service;

import com.baomidou.mybatisplus.core.conditions.query.Wrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.ets.delivery.application.infra.entity.riskreview.RiskReviewsOrder;
import com.ets.delivery.application.infra.mapper.RiskReviewsOrderMapper;
import org.springframework.stereotype.Service;

/**
 * <p>
 * 风控审核单记录表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
@Service
public class RiskReviewsOrderService extends BaseService<RiskReviewsOrderMapper, RiskReviewsOrder> {

    /**
     * 根据业务单号查询风控审核单
     *
     * @param businessSn 业务单号
     * @return 风控审核单
     */
    public RiskReviewsOrder getByBusinessSn(String businessSn) {
        Wrapper<RiskReviewsOrder> wrapper = Wrappers.<RiskReviewsOrder>lambdaQuery()
                .eq(RiskReviewsOrder::getBusinessSn, businessSn)
                .last("limit 1");
        return this.baseMapper.selectOne(wrapper);
    }

    /**
     * 根据风控单号查询风控审核单
     *
     * @param riskOrderSn 风控单号
     * @return 风控审核单
     */
    public RiskReviewsOrder getByRiskOrderSn(String riskOrderSn) {
        Wrapper<RiskReviewsOrder> wrapper = Wrappers.<RiskReviewsOrder>lambdaQuery()
                .eq(RiskReviewsOrder::getRiskOrderSn, riskOrderSn)
                .last("limit 1");
        return this.baseMapper.selectOne(wrapper);
    }
}
