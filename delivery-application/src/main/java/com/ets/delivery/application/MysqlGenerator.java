package com.ets.delivery.application;
import com.baomidou.mybatisplus.generator.FastAutoGenerator;
import com.baomidou.mybatisplus.generator.config.rules.NamingStrategy;
import com.baomidou.mybatisplus.generator.engine.FreemarkerTemplateEngine;
import com.ets.delivery.application.infra.entity.BaseEntity;
import com.ets.delivery.application.infra.mapper.CommonBaseMapper;
import com.ets.delivery.application.infra.service.BaseService;
import com.ets.starter.generator.MySqlTypeConvertCustom;
import org.apache.ibatis.annotations.Mapper;
import org.springframework.stereotype.Repository;


public class MysqlGenerator {

    public static void main(String[] args) {

        String packageName = "com.ets.delivery.application.infra";
        String outputDir = "";
        String author = "kit";
        String password = "dev!123yEs";
        String username = "etc_dev";
        String dbUrl = "*********************************************************************************************************************";
        String[] table = {"etc_risk_reviews_order", "etc_risk_reviews_order_info", "etc_risk_reviews_log"};
        String tablePrefix = "etc_";

        FastAutoGenerator.create(dbUrl, username, password)
                .dataSourceConfig(builder -> builder.typeConvert(MySqlTypeConvertCustom.INSTANCE))
                .globalConfig(
                        builder -> builder.author(author)
                                .outputDir(outputDir)
                                .commentDate("yyyy-MM-dd")
                ).packageConfig(
                        builder  -> builder.parent(packageName)
                                .entity("entity")
                                .mapper("mapper")
                                .service("service")
                                .xml("mapper.xml")
                ).strategyConfig(
                        builder -> builder.entityBuilder()
                                .enableLombok()
                                .superClass(BaseEntity.class)
                                .naming(NamingStrategy.underline_to_camel)
                                .columnNaming(NamingStrategy.underline_to_camel)
                                .enableFileOverride()
                ).strategyConfig(
                        builder -> builder.serviceBuilder()
                                .superServiceImplClass(BaseService.class)
                                .formatServiceImplFileName("%sService")
                                .enableFileOverride()
                                .disableService()
                ).strategyConfig(
                        builder -> builder.mapperBuilder()
                                .superClass(CommonBaseMapper.class)
                                .enableBaseResultMap()
                                .formatMapperFileName("%sMapper")
                                .mapperAnnotation(Mapper.class)
                                .enableFileOverride()
                ).strategyConfig(
                        builder -> builder.addInclude(table)
                                .addTablePrefix(tablePrefix)
                )
                .templateEngine(new FreemarkerTemplateEngine())
                .execute();


        System.exit(0);
    }

}