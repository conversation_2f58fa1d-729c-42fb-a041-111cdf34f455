package com.ets.delivery.application.common.consts.reviews;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 申办审核单风控状态枚举
 */
@Getter
@AllArgsConstructor
public enum ReviewsRiskStatusEnum {
    
    /**
     * 待风控
     */
    PENDING(0, "待风控"),
    
    /**
     * 风控中
     */
    IN_PROGRESS(1, "风控中"),
    
    /**
     * 风控通过
     */
    APPROVED(2, "风控通过"),
    
    /**
     * 风控拒绝
     */
    REJECTED(3, "风控拒绝"),
    
    /**
     * 风控取消
     */
    CANCELED(4, "风控取消");

    private final Integer value;
    private final String desc;

    public static final Map<Integer, String> map;
    static {
        map = Arrays.stream(ReviewsRiskStatusEnum.values()).collect(Collectors.toMap(ReviewsRiskStatusEnum::getValue, ReviewsRiskStatusEnum::getDesc));
    }

    /**
     * 根据值获取枚举
     *
     * @param value 枚举值
     * @return 枚举对象
     */
    public static ReviewsRiskStatusEnum getByValue(Integer value) {
        if (value == null) {
            return null;
        }
        return Arrays.stream(ReviewsRiskStatusEnum.values())
                .filter(enumValue -> enumValue.getValue().equals(value))
                .findFirst()
                .orElse(null);
    }

    /**
     * 根据值获取描述
     *
     * @param value 枚举值
     * @return 描述
     */
    public static String getDescByValue(Integer value) {
        ReviewsRiskStatusEnum enumValue = getByValue(value);
        return enumValue != null ? enumValue.getDesc() : null;
    }

    /**
     * 验证值是否有效
     *
     * @param value 枚举值
     * @return 是否有效
     */
    public static boolean isValidValue(Integer value) {
        return getByValue(value) != null;
    }
}
