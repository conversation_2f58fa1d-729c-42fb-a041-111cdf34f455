package com.ets.delivery.application.common.dto.riskreview;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 创建风险审核订单请求参数
 */
@Data
public class RiskReviewOrderCreateDTO {

    /**
     * 业务单号
     */
    @NotBlank(message = "业务单号不能为空")
    private String businessSn;

    /**
     * 风控流水号
     */
    @NotBlank(message = "风控流水号不能为空")
    private String riskSn;

    /**
     * 风控类型[1-初审 2-复审]
     */
    @NotNull(message = "风控类型不能为空")
    private Integer riskType;

    /**
     * 业务类型[1-申办]
     */
    @NotNull(message = "业务类型不能为空")
    private Integer businessType;

    /**
     * 发卡方id
     */
    @NotNull(message = "发卡方id不能为空")
    private Integer issuerId;

    /**
     * 用户id
     */
    @NotNull(message = "用户id不能为空")
    private Long uid;

    /**
     * 车牌号
     */
    @NotBlank(message = "车牌号不能为空")
    private String plateNo;

    /**
     * 车牌颜色
     */
    @NotNull(message = "车牌颜色不能为空")
    private Integer plateColor;
}
