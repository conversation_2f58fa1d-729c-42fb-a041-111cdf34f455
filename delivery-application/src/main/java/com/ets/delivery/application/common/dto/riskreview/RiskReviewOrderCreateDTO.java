package com.ets.delivery.application.common.dto.riskreview;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 创建风险审核订单请求参数
 */
@Data
public class RiskReviewOrderCreateDTO {

    /**
     * 订单流水号
     */
    @NotBlank(message = "订单流水号不能为空")
    private String orderSn;

    /**
     * 业务类型
     */
    @NotBlank(message = "业务类型不能为空")
    private String orderType;

    /**
     * 发卡方id
     */
    @NotNull(message = "发卡方id不能为空")
    private Integer issuerId;

    /**
     * 用户id
     */
    @NotNull(message = "用户id不能为空")
    private Long uid;

    /**
     * 车牌号
     */
    @NotBlank(message = "车牌号不能为空")
    private String plateNo;

    /**
     * 车牌颜色
     */
    @NotNull(message = "车牌颜色不能为空")
    private Integer plateColor;

    /**
     * 风险类型
     */
    @NotBlank(message = "风险类型不能为空")
    private String riskType;

    /**
     * 风险等级
     */
    @NotNull(message = "风险等级不能为空")
    private Integer riskLevel;

    /**
     * 风险描述
     */
    private String riskDescription;

    /**
     * 回调通知地址
     */
    private String notifyUrl;

    /**
     * 风险证据信息
     */
    private RiskEvidenceDTO riskEvidence;

    /**
     * 风险证据信息数据类型
     */
    @Data
    public static class RiskEvidenceDTO {
        /**
         * 风险截图
         */
        private String riskScreenshotUrl;

        /**
         * 风险视频
         */
        private String riskVideoUrl;

        /**
         * 风险报告文件
         */
        private String riskReportUrl;

        /**
         * 其他证据文件
         */
        private String otherEvidenceUrl;

        /**
         * 证据描述
         */
        private String evidenceDescription;
    }
}
