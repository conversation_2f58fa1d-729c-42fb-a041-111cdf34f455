package com.ets.delivery.application.infra.service;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.Wrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ets.delivery.application.common.bo.PostReviewListBO;
import com.ets.delivery.application.common.consts.reviews.PushIssuerStatusEnum;
import com.ets.delivery.application.common.consts.reviews.ReviewReviewStatusEnum;
import com.ets.delivery.application.common.consts.reviews.ReviewStatusEnum;
import com.ets.delivery.application.infra.entity.Reviews;
import com.ets.delivery.application.infra.mapper.ReviewsMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <p>
 * 审核单列表 服务实现类
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-02
 */
@Service
@DS("db-issuer-admin")
public class ReviewsService extends BaseService<ReviewsMapper, Reviews> {

    public IPage<Reviews> getPostReviewByReviewSnList(PostReviewListBO listBO) {
        Wrapper<Reviews> wrapper = Wrappers.<Reviews>lambdaQuery()
                .eq(Reviews::getAutoAudit, 1)
                .in(Reviews::getReviewSn, listBO.getReviewSnList())
                .orderBy(true, listBO.getSort().equals("asc"), Reviews::getCreatedAt);
        return this.baseMapper.selectPage(new Page<>(listBO.getPageNum(), listBO.getPageSize()), wrapper);
    }

    public List<Reviews> getAutoAuditList(String orderSn) {
        Wrapper<Reviews> wrapper = Wrappers.<Reviews>lambdaQuery()
                .eq(Reviews::getAutoAudit, 1)
                .eq(Reviews::getStatus, ReviewStatusEnum.STATUS_NORMAL.getValue())
                .eq(Reviews::getReviewStatus, ReviewReviewStatusEnum.REVIEW_STATUS_PASS.getValue())
                .eq(Reviews::getPushIssuerStatus, PushIssuerStatusEnum.PUSH_ISSUER_STATUS_SUCCESS.getValue())
                .eq(StringUtils.isNotEmpty(orderSn), Reviews::getOrderSn, orderSn)
                .orderByAsc(Reviews::getCreatedAt);
        return this.baseMapper.selectList(wrapper);
    }

    public Reviews getOneByReviewSn(String reviewSn) {
        Wrapper<Reviews> wrapper = Wrappers.<Reviews>lambdaQuery()
                .eq(Reviews::getReviewSn, reviewSn)
                .last("limit 1");
        return this.baseMapper.selectOne(wrapper);
    }

    /**
     * 根据订单流水号查询申办审核单
     *
     * @param orderSn 订单流水号（业务单号）
     * @return 申办审核单
     */
    public Reviews getOneByOrderSn(String orderSn) {
        Wrapper<Reviews> wrapper = Wrappers.<Reviews>lambdaQuery()
                .eq(Reviews::getOrderSn, orderSn)
                .eq(Reviews::getAutoAudit, 1)
                .orderByDesc(Reviews::getCreatedAt)
                .last("limit 1");
        return this.baseMapper.selectOne(wrapper);
    }
}
