package com.ets.delivery.application.common.dto.logistics;

import lombok.Data;

import jakarta.validation.constraints.NotEmpty;
import jakarta.validation.constraints.NotNull;

@Data
public class LogisticsDeliverDTO {

    @NotNull(message = "id不能为空")
    private Integer logisticsId;

    @NotEmpty(message = "快递公司不能为空")
    private String expressCorp;

    @NotEmpty(message = "快递单号不能为空")
    private String expressNumber;

    private String deliveryRemark;
}
