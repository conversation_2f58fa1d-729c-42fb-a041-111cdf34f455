package com.ets.delivery.application.common.consts.riskreview;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 风控审核状态枚举
 */
@Getter
@AllArgsConstructor
public enum RiskReviewStatusEnum {
    
    /**
     * 待审核
     */
    PENDING(0, "待审核"),
    
    /**
     * 审核通过
     */
    APPROVED(1, "审核通过"),
    
    /**
     * 审核驳回取消
     */
    REJECTED_CANCEL(2, "审核驳回取消"),
    
    /**
     * 审核驳回重新上传
     */
    REJECTED_REUPLOAD(3, "审核驳回重新上传"),
    
    /**
     * 补传资料
     */
    SUPPLEMENT_MATERIAL(4, "补传资料"),
    
    /**
     * 审核取消
     */
    CANCELED(5, "审核取消");

    private final Integer value;
    private final String desc;

    public static final Map<Integer, String> map;
    static {
        map = Arrays.stream(RiskReviewStatusEnum.values()).collect(Collectors.toMap(RiskReviewStatusEnum::getValue, RiskReviewStatusEnum::getDesc));
    }

    public static boolean isValidValue(Integer value) {
        if (value == null) {
            return false;
        }
        return Arrays.stream(RiskReviewStatusEnum.values())
                .anyMatch(enumValue -> enumValue.getValue().equals(value));
    }
}
