package com.ets.delivery.application.common.vo.storageMap;

import com.ets.delivery.application.common.consts.storage.StorageCodeEnum;
import lombok.Data;

import java.util.List;

@Data
public class StorageMapAddressDetailVO {

    /**
     * 配置ID
     */
    private Integer configId;

    /**
     * 配置名称
     */
    private String configName;

    /**
     * 默认仓储代号
     */
    private String defaultStorageCode;

    private String logisticsCode;

    private List<Rule> rules;

    @Data
    public static class Rule {

        private String province;

        private String city;

        private String area;

        /**
         * 规则映射仓储代号
         */
        private String storageCode;
        private String storageCodeStr;

        public String getStorageCodeStr() {
            return StorageCodeEnum.map.getOrDefault(storageCode, "");
        }
    }
}
