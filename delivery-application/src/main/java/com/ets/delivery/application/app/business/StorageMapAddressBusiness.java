package com.ets.delivery.application.app.business;

import cn.hutool.core.bean.BeanUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.ets.common.JsonResult;
import com.ets.common.ToolsHelper;
import com.ets.common.util.ListUtils;
import com.ets.common.util.ThreadLocalUtil;
import com.ets.delivery.application.app.thirdservice.feign.ApplyFeign;
import com.ets.delivery.application.app.thirdservice.request.apply.ApplyPackageListDTO;
import com.ets.delivery.application.common.bo.StorageMapAddressRuleBO;
import com.ets.delivery.application.common.consts.StorageMapAddressCacheKey;
import com.ets.delivery.application.common.consts.ThreadLocalCacheKey;
import com.ets.delivery.application.common.consts.storageMap.StorageMapAddressStatusEnum;
import com.ets.delivery.application.common.dto.storageMap.*;
import com.ets.delivery.application.common.vo.addressMap.AddressMapInfoVO;
import com.ets.delivery.application.common.vo.storageMap.StorageMapAddressDetailVO;
import com.ets.delivery.application.common.vo.storageMap.StorageMapAddressListVO;
import com.ets.delivery.application.common.vo.storageMap.StorageMapAddressProductPackageListVO;
import com.ets.delivery.application.infra.entity.*;
import com.ets.delivery.application.infra.service.AddressStorageMapConfigService;
import com.ets.delivery.application.infra.service.AddressStorageMapLogService;
import com.ets.delivery.application.infra.service.CacheMapService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;


import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Component
public class StorageMapAddressBusiness {

    @Autowired
    private AddressStorageMapConfigService configService;

    @Autowired
    private AddressStorageMapLogService logService;

    @Autowired
    private CacheMapService cacheMapService;

    @Resource(name = "redisPermanentTemplate")
    private StringRedisTemplate redisPermanentTemplate;

    @Autowired
    private ApplyFeign applyFeign;

    public IPage<StorageMapAddressListVO> getList(StorageMapAddressListDTO listDTO) {
        LambdaQueryWrapper<AddressStorageMapConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.ne(AddressStorageMapConfig::getStatus, StorageMapAddressStatusEnum.STATUS_DELETE.getValue())
                .orderByDesc(AddressStorageMapConfig::getCreatedAt);
        IPage<AddressStorageMapConfig> pageList = configService.getPageListByWrapper(new Page<>(listDTO.getPageNum(), listDTO.getPageSize()), wrapper);
        return pageList.convert(config -> BeanUtil.copyProperties(config, StorageMapAddressListVO.class));
    }

    public StorageMapAddressDetailVO getDetail(StorageMapAddressDetailDTO detailDTO) {
        AddressStorageMapConfig config = configService.getById(detailDTO.getConfigId());
        if (ObjectUtils.isEmpty(config) || config.getStatus().equals(StorageMapAddressStatusEnum.STATUS_DELETE.getValue())) {
            ToolsHelper.throwException("推荐规则不存在");
        }

        // 处理地址查询条件
        StringBuilder address = new StringBuilder();
        if (StringUtils.isNotEmpty(detailDTO.getProvince())) {
            address.append(detailDTO.getProvince());
        }
        if (StringUtils.isNotEmpty(detailDTO.getCity())) {
            address.append(" ").append(detailDTO.getCity());
        }
        if (StringUtils.isNotEmpty(detailDTO.getArea())) {
            address.append(" ").append(detailDTO.getArea());
        }

        // 查询规则
        LambdaQueryWrapper<CacheMap> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CacheMap::getCacheType, "preview")
                .eq(CacheMap::getCacheMainKey, StorageMapAddressCacheKey.STORAGE_MAP_ADDRESS_PREFIX + ":" + config.getId())
                .like(StringUtils.isNotEmpty(address), CacheMap::getCacheKey, address.toString())
                .orderByDesc(CacheMap::getCacheKey);
        List<CacheMap> mapList = cacheMapService.getListByWrapper(wrapper);
        List<StorageMapAddressRuleBO> ruleBOList = cacheMapToRules(mapList);

        List<StorageMapAddressDetailVO.Rule> ruleList = new ArrayList<>();
        if (ObjectUtils.isNotEmpty(ruleBOList)) {
            ruleList = ListUtils.convertByClass(ruleBOList, StorageMapAddressDetailVO.Rule.class);
        }

        StorageMapAddressDetailVO detailVO = new StorageMapAddressDetailVO();
        detailVO.setConfigId(config.getId());
        detailVO.setConfigName(config.getConfigName());
        detailVO.setDefaultStorageCode(config.getDefaultStorageCode());
        detailVO.setLogisticsCode(config.getLogisticsCode());
        detailVO.setRules(ruleList);

        return detailVO;
    }

    public void create(StorageMapAddressCreateDTO createDTO) {
        // 检查重复规则
        if (ObjectUtils.isNotEmpty(createDTO.getRules())) {
            checkAddressRepeat(createDTO.getRules());
        }

        User user = (User) ThreadLocalUtil.getData(ThreadLocalCacheKey.LOGIN_USER_KEY);
        // 创建配置
        AddressStorageMapConfig mapConfig = new AddressStorageMapConfig();
        mapConfig.setConfigName(createDTO.getConfigName());
        mapConfig.setDefaultStorageCode(createDTO.getDefaultStorageCode());
        mapConfig.setLogisticsCode(createDTO.getLogisticsCode());
        mapConfig.setOperator(user.getUsername());
        mapConfig.setStatus(StorageMapAddressStatusEnum.STATUS_INVALID.getValue());
        configService.create(mapConfig);

        // 创建规则记录
        if (ObjectUtils.isNotEmpty(createDTO.getRules())) {
            Set<CacheMap> cacheMapList = rulesToCacheMap(mapConfig.getId(), createDTO.getRules());
            cacheMapService.saveBatch(cacheMapList);
        }

        // 记录日志
        AddressStorageMapLog log = new AddressStorageMapLog();
        log.setConfigId(mapConfig.getId());
        log.setOperator(user.getUsername());
        log.setOperateDesc("create");
        log.setAfterOperate(JSON.toJSONString(createDTO.getRules()));
        logService.create(log);
    }

    public void edit(StorageMapAddressEditDTO editDTO) {
        // 检查重复规则
        if (ObjectUtils.isNotEmpty(editDTO.getRules())) {
            checkAddressRepeat(editDTO.getRules());
        }

        User user = (User) ThreadLocalUtil.getData(ThreadLocalCacheKey.LOGIN_USER_KEY);

        AddressStorageMapConfig mapConfig = configService.getById(editDTO.getConfigId());
        if (ObjectUtils.isEmpty(mapConfig) || mapConfig.getStatus().equals(StorageMapAddressStatusEnum.STATUS_DELETE.getValue())) {
            ToolsHelper.throwException("推荐规则不存在");
        }

        // 查询历史规则
        LambdaQueryWrapper<CacheMap> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(CacheMap::getCacheType, "preview")
                .eq(CacheMap::getCacheMainKey, StorageMapAddressCacheKey.STORAGE_MAP_ADDRESS_PREFIX + ":" + mapConfig.getId());
        List<CacheMap> mapList = cacheMapService.getListByWrapper(wrapper);

        List<StorageMapAddressRuleBO> ruleList = cacheMapToRules(mapList);

        // 更新规则记录
        cacheMapService.remove(wrapper);

        if (ObjectUtils.isNotEmpty(editDTO.getRules())) {
            Set<CacheMap> cacheMapList = rulesToCacheMap(mapConfig.getId(), editDTO.getRules());
            cacheMapService.saveBatch(cacheMapList);
        }

        // 更新配置状态
        mapConfig.setDefaultStorageCode(editDTO.getDefaultStorageCode());
        mapConfig.setLogisticsCode(editDTO.getLogisticsCode());
        mapConfig.setConfigName(editDTO.getConfigName());
        mapConfig.setOperator(user.getUsername());
        mapConfig.setStatus(StorageMapAddressStatusEnum.STATUS_INVALID.getValue());
        configService.updateById(mapConfig);

        // 记录日志
        AddressStorageMapLog log = new AddressStorageMapLog();
        log.setConfigId(mapConfig.getId());
        log.setOperator(user.getUsername());
        log.setOperateDesc("edit");
        log.setPreOperate(JSON.toJSONString(ruleList));
        log.setAfterOperate(JSON.toJSONString(editDTO.getRules()));
        logService.create(log);
    }

    public void delete(StorageMapAddressDeleteDTO deleteDTO) {
        User user = (User) ThreadLocalUtil.getData(ThreadLocalCacheKey.LOGIN_USER_KEY);
        AddressStorageMapConfig mapConfig = configService.getById(deleteDTO.getConfigId());
        if (ObjectUtils.isEmpty(mapConfig)) {
            ToolsHelper.throwException("推荐规则不存在");
        }
        if (mapConfig.getStatus().equals(StorageMapAddressStatusEnum.STATUS_DELETE.getValue())) {
            return;
        }

        if (mapConfig.getIsDefault() == 1) {
            ToolsHelper.throwException("默认推荐规则不允许删除");
        }

        // 查询历史规则
        LambdaQueryWrapper<CacheMap> prodWrapper = new LambdaQueryWrapper<>();
        prodWrapper.eq(CacheMap::getCacheType, "prod")
                .eq(CacheMap::getCacheMainKey, StorageMapAddressCacheKey.STORAGE_MAP_ADDRESS_PREFIX + ":" + mapConfig.getId());
        List<CacheMap> prodMapList = cacheMapService.getListByWrapper(prodWrapper);
        List<StorageMapAddressRuleBO> ruleBOList = cacheMapToRules(prodMapList);

        LambdaQueryWrapper<CacheMap> previewWrapper = new LambdaQueryWrapper<>();
        previewWrapper.eq(CacheMap::getCacheType, "preview")
                .eq(CacheMap::getCacheMainKey, StorageMapAddressCacheKey.STORAGE_MAP_ADDRESS_PREFIX + ":" + mapConfig.getId());

        // 删除规则配置
        cacheMapService.remove(prodWrapper);
        cacheMapService.remove(previewWrapper);

        mapConfig.setStatus(StorageMapAddressStatusEnum.STATUS_DELETE.getValue());
        mapConfig.setOperator(user.getUsername());
        mapConfig.setUpdatedAt(LocalDateTime.now());
        configService.updateById(mapConfig);

        // 删除缓存
        Set<String> keys = redisPermanentTemplate.keys(StorageMapAddressCacheKey.STORAGE_MAP_ADDRESS_PREFIX + ":" + mapConfig.getId() + "*");
        if (keys != null) {
            for (String key : keys) {
                redisPermanentTemplate.delete(key);
            }
        }

        // 记录日志
        AddressStorageMapLog log = new AddressStorageMapLog();
        log.setConfigId(mapConfig.getId());
        log.setOperator(user.getUsername());
        log.setOperateDesc("delete");
        log.setPreOperate(ObjectUtils.isNotEmpty(ruleBOList) ? JSON.toJSONString(ruleBOList) : "");
        log.setAfterOperate("");
        logService.create(log);
    }

    public void release() {
        User user = (User) ThreadLocalUtil.getData(ThreadLocalCacheKey.LOGIN_USER_KEY);
        // 查询未生效配置
        LambdaQueryWrapper<AddressStorageMapConfig> configWrapper = new LambdaQueryWrapper<>();
        configWrapper.eq(AddressStorageMapConfig::getStatus, StorageMapAddressStatusEnum.STATUS_INVALID.getValue());
        List<AddressStorageMapConfig> configList = configService.getListByWrapper(configWrapper);
        if (ObjectUtils.isEmpty(configList)) {
            return;
        }

        // 生成生产规则记录
        configList.forEach(config -> {
            // 删除旧规则
            LambdaQueryWrapper<CacheMap> prodWrapper = new LambdaQueryWrapper<>();
            prodWrapper.eq(CacheMap::getCacheType, "prod")
                    .eq(CacheMap::getCacheMainKey, StorageMapAddressCacheKey.STORAGE_MAP_ADDRESS_PREFIX + ":" + config.getId());
            cacheMapService.remove(prodWrapper);

            // 清掉旧规则缓存
            Set<String> keys = redisPermanentTemplate.keys(StorageMapAddressCacheKey.STORAGE_MAP_ADDRESS_PREFIX + ":" + config.getId() + "*");
            if (keys != null) {
                for (String key : keys) {
                    redisPermanentTemplate.delete(key);
                }
            }

            // 生成新规则
            LambdaQueryWrapper<CacheMap> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(CacheMap::getCacheType, "preview")
                    .eq(CacheMap::getCacheMainKey, StorageMapAddressCacheKey.STORAGE_MAP_ADDRESS_PREFIX + ":" + config.getId());
            List<CacheMap> cacheMapList = cacheMapService.getListByWrapper(wrapper);
            List<CacheMap> newCacheMapList = new ArrayList<>();
            cacheMapList.forEach(map -> {
                CacheMap newMap = new CacheMap();
                newMap.setCacheType("prod");
                newMap.setCacheMainKey(map.getCacheMainKey());
                newMap.setCacheKey(map.getCacheKey());
                newMap.setCacheValue(map.getCacheValue());
                newMap.setCreatedAt(LocalDateTime.now());
                newMap.setUpdatedAt(LocalDateTime.now());
                newCacheMapList.add(newMap);

                // 生成缓存
                redisPermanentTemplate.opsForValue().set(map.getCacheMainKey() + ":" + map.getCacheKey(), map.getCacheValue());
                redisPermanentTemplate.expire(map.getCacheMainKey() + ":" + map.getCacheKey(), 7, java.util.concurrent.TimeUnit.DAYS);
            });
            cacheMapService.saveBatch(newCacheMapList);

            // 更新配置状态
            config.setStatus(StorageMapAddressStatusEnum.STATUS_VALID.getValue());
            config.setOperator(user.getUsername());
            config.setUpdatedAt(LocalDateTime.now());
            configService.updateById(config);

            // 记录日志
            AddressStorageMapLog log = new AddressStorageMapLog();
            log.setConfigId(config.getId());
            log.setOperator(user.getUsername());
            log.setOperateDesc("release");
            log.setAfterOperate(JSON.toJSONString(cacheMapToRules(newCacheMapList)));
            logService.create(log);
        });
    }

    public void setDefault(StorageMapAddressSetDefaultDTO setDefaultDTO) {
        User user = (User) ThreadLocalUtil.getData(ThreadLocalCacheKey.LOGIN_USER_KEY);

        AddressStorageMapConfig mapConfig = configService.getById(setDefaultDTO.getConfigId());
        if (ObjectUtils.isEmpty(mapConfig) || mapConfig.getStatus().equals(StorageMapAddressStatusEnum.STATUS_DELETE.getValue())) {
            ToolsHelper.throwException("推荐规则不存在");
        }

        if (mapConfig.getIsDefault() == 1) {
            return;
        }

        // 查询默认配置 更新为非默认
        AddressStorageMapConfig defaultConfig = configService.getDefaultConfig();
        if (ObjectUtils.isNotEmpty(defaultConfig)) {
            defaultConfig.setIsDefault(0);
            defaultConfig.setOperator(user.getUsername());
            defaultConfig.setUpdatedAt(LocalDateTime.now());
            configService.updateById(defaultConfig);

            // 记录日志
            AddressStorageMapLog defaultLog = new AddressStorageMapLog();
            defaultLog.setConfigId(defaultConfig.getId());
            defaultLog.setOperator(user.getUsername());
            defaultLog.setOperateDesc("setDefault");
            defaultLog.setAfterOperate("取消默认推荐规则");
            logService.create(defaultLog);
        }

        // 设置为默认
        mapConfig.setIsDefault(1);
        mapConfig.setOperator(user.getUsername());
        mapConfig.setUpdatedAt(LocalDateTime.now());
        configService.updateById(mapConfig);

        // 记录日志
        AddressStorageMapLog log = new AddressStorageMapLog();
        log.setConfigId(mapConfig.getId());
        log.setOperator(user.getUsername());
        log.setOperateDesc("setDefault");
        log.setAfterOperate("设置为默认推荐规则");
        logService.create(log);
    }

    private Set<CacheMap> rulesToCacheMap(Integer configId, List<StorageMapAddressRuleBO> ruleBOList) {
        if (ObjectUtils.isEmpty(ruleBOList)) {
            return null;
        }
        Set<CacheMap> cacheMapList = new HashSet<>();

        // 按仓储代号分组
        Map<String, List<StorageMapAddressRuleBO>> listMap = ruleBOList.stream()
                .collect(Collectors.groupingBy(StorageMapAddressRuleBO::getStorageCode));

        // 创建规则
        listMap.forEach((storageCode, ruleList) -> ruleList.forEach(rule -> {
            // 生成规则记录
            CacheMap cacheMap = new CacheMap();
            cacheMap.setCacheType("preview");
            cacheMap.setCacheMainKey(StorageMapAddressCacheKey.STORAGE_MAP_ADDRESS_PREFIX + ":" + configId);
            cacheMap.setCacheValue(storageCode);
            cacheMap.setCreatedAt(LocalDateTime.now());
            cacheMap.setUpdatedAt(LocalDateTime.now());

            if (StringUtils.isEmpty(rule.getCity()) && StringUtils.isEmpty(rule.getArea())) {
                cacheMap.setCacheKey(rule.getProvince());
            } else if (StringUtils.isEmpty(rule.getArea())) {
                cacheMap.setCacheKey(rule.getProvince() + " " + rule.getCity());
            } else {
                cacheMap.setCacheKey(rule.getProvince() + " " + rule.getCity() + " " + rule.getArea());
            }
            cacheMapList.add(cacheMap);
        }));
        return cacheMapList;
    }

    private List<StorageMapAddressRuleBO> cacheMapToRules(List<CacheMap> cacheMapList) {
        if (ObjectUtils.isEmpty(cacheMapList)) {
            return null;
        }

        List<StorageMapAddressRuleBO> ruleList = new ArrayList<>();
        cacheMapList.forEach(map -> {
            StorageMapAddressRuleBO rule = new StorageMapAddressRuleBO();
            rule.setStorageCode(map.getCacheValue());
            String[] addressArr = map.getCacheKey().split(" ");
            if (addressArr.length == 1) {
                // 处理省份
                rule.setProvince(addressArr[0]);
            } else if (addressArr.length == 2) {
                // 处理城市
                rule.setProvince(addressArr[0]);
                rule.setCity(addressArr[1]);
            } else if (addressArr.length == 3) {
                // 处理区县
                rule.setProvince(addressArr[0]);
                rule.setCity(addressArr[1]);
                rule.setArea(addressArr[2]);
            }
            ruleList.add(rule);
        });

        return ruleList;
    }

    private void checkAddressRepeat(List<StorageMapAddressRuleBO> ruleBOList) {
        // 检查地址是否有重复数据
        Map<String, String> repeatMap = new HashMap<>();
        ruleBOList.forEach(rule -> {
            String key = rule.getProvince() + rule.getCity() + rule.getArea();
            String storageCode = repeatMap.get(key);
            if (StringUtils.isNotEmpty(storageCode)) {
                ToolsHelper.throwException("地址：【" + key + "】存在重复配置");
            }
            repeatMap.put(key, rule.getStorageCode());
        });
    }

    public AddressMapInfoVO addressMapStorageCode(String area, Integer configId) {
        AddressMapInfoVO vo = new AddressMapInfoVO();
        if (configId == null || configId == 0) {
            AddressStorageMapConfig storageMapConfig = configService.getDefaultConfig();
            if (ObjectUtils.isEmpty(storageMapConfig)) {
                ToolsHelper.throwException("未配置地址仓储映射规则");
            }
            configId = storageMapConfig.getId();
        }
        AddressStorageMapConfig mapConfig = configService.getById(configId);
        if (ObjectUtils.isEmpty(mapConfig) || mapConfig.getStatus().equals(StorageMapAddressStatusEnum.STATUS_DELETE.getValue())) {
            ToolsHelper.throwException("推荐规则不存在");
        }
        // 默认仓储
        vo.setStorageCode( mapConfig.getDefaultStorageCode());
        vo.setLogisticsCode(mapConfig.getLogisticsCode());

        // 查询缓存
        String[] areaArr = area.trim().split("\\s+");
        String cacheKeyPrefix = StorageMapAddressCacheKey.STORAGE_MAP_ADDRESS_PREFIX + ":" + configId + ":";
        Set<String> keys = redisPermanentTemplate.keys(cacheKeyPrefix + "*");

        // 缓存不存在 生成缓存
        if (ObjectUtils.isEmpty(keys)) {
            LambdaQueryWrapper<CacheMap> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(CacheMap::getCacheType, "prod")
                    .eq(CacheMap::getCacheMainKey, StorageMapAddressCacheKey.STORAGE_MAP_ADDRESS_PREFIX + ":" + configId);
            List<CacheMap> cacheMapList = cacheMapService.getListByWrapper(wrapper);
            cacheMapList.forEach(cacheMap -> {
                redisPermanentTemplate.opsForValue().set(cacheMap.getCacheMainKey() + ":" + cacheMap.getCacheKey(), cacheMap.getCacheValue());
                redisPermanentTemplate.expire(cacheMap.getCacheMainKey() + ":" + cacheMap.getCacheKey(), 7, java.util.concurrent.TimeUnit.DAYS);
            });
        }

        // 省市区
        String cacheKey = cacheKeyPrefix + area;
        String storageCode = redisPermanentTemplate.opsForValue().get(cacheKey);
        if (StringUtils.isNotEmpty(storageCode)) {
            vo.setStorageCode(storageCode);
            return vo;
        }
        // 省市
        cacheKey = cacheKeyPrefix + areaArr[0] + " " + areaArr[1];
        storageCode = redisPermanentTemplate.opsForValue().get(cacheKey);
        if (StringUtils.isNotEmpty(storageCode)) {
            vo.setStorageCode(storageCode);
            return vo;
        }
        // 省
        cacheKey = cacheKeyPrefix + areaArr[0];
        storageCode = redisPermanentTemplate.opsForValue().get(cacheKey);
        if (StringUtils.isNotEmpty(storageCode)) {
            vo.setStorageCode(storageCode);
            return vo;
        }
        return vo;
    }

    public IPage<StorageMapAddressProductPackageListVO> getProductPackageList(StorageMapAddressProductPackageListDTO listDTO) {
        ApplyPackageListDTO applyPackageListDTO = BeanUtil.copyProperties(listDTO, ApplyPackageListDTO.class);
        JsonResult<?> result = applyFeign.getProductPackageList(applyPackageListDTO);
        result.checkError();
        IPage<StorageMapAddressProductPackageListVO> page = new Page<>();
        BeanUtil.copyProperties(result.getData(), page);
        return page;
    }
}
