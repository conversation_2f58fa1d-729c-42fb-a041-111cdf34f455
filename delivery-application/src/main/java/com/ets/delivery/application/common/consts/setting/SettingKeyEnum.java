package com.ets.delivery.application.common.consts.setting;

import lombok.Getter;

import java.util.Arrays;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

@Getter
public enum SettingKeyEnum {

    VEHICLE_TYPE("vehicle_type", "车辆类型"),
    USE_CHARACTER("use_character", "使用性质"),
    CANCEL_ORDER("cancel_order", "取消订单"),
    REPEAT_UPLOAD("repeat_upload", "重新上传"),
    REPEAT_UPLOAD_CONCAT("repeat_upload_concat", "拼接重传"),
    AFTER_SALES_REVIEW("after_sales_review", "售后审核"),
    ;

    private final String value;
    private final String desc;
    public static final Map<String, String> map;
    public static final List<String> list;


    SettingKeyEnum(String value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    static {
        SettingKeyEnum[] enums = SettingKeyEnum.values();
        int size = enums.length;
        map = IntStream.range(0, size).collect(LinkedHashMap::new,
                (map, index) -> map.put(enums[index].getValue(), enums[index].getDesc()),
                Map::putAll);
        list = Arrays.stream(enums).map(SettingKeyEnum::getValue).collect(Collectors.toList());
    }
}
