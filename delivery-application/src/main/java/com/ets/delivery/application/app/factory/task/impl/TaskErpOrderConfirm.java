package com.ets.delivery.application.app.factory.task.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.ObjectUtil;
import com.alibaba.fastjson.JSON;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ets.delivery.application.app.business.ErpOrderBusiness;
import com.ets.delivery.application.app.factory.task.TaskFactory;
import com.ets.delivery.application.app.thirdservice.request.yunda.DeliveryConfirmDTO;
import com.ets.delivery.application.common.bo.erp.ErpLogisticsSkuBO;
import com.ets.delivery.application.common.bo.task.TaskLogisticsConfirmYundaBO;
import com.ets.delivery.application.common.consts.erpRecord.ErpRecordOrderSourceEnum;
import com.ets.delivery.application.common.consts.erpRecord.ErpRecordOrderTypeEnum;
import com.ets.delivery.application.common.consts.erpRecord.ErpRecordStatusEnum;
import com.ets.delivery.application.common.consts.taskRecord.TaskRecordReferTypeEnum;
import com.ets.delivery.application.common.consts.yunda.YundaDeliveryOrderOrderTypeEnum;
import com.ets.delivery.application.common.consts.yunda.YundaLogiticsCodeEnum;
import com.ets.delivery.application.common.dto.logistics.LogisticsOrderConfirmDTO;
import com.ets.delivery.application.common.dto.task.TaskRecordDTO;
import com.ets.delivery.application.infra.entity.ErpRecord;
import com.ets.delivery.application.infra.entity.TaskRecord;
import com.ets.delivery.application.infra.service.ErpRecordService;
import com.ets.delivery.application.infra.service.TaskRecordService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

@Slf4j
@Component
public class TaskErpOrderConfirm extends TaskBase {

    @Autowired
    private TaskRecordService taskRecordService;

    @Autowired
    private ErpRecordService erpRecordService;

    @Autowired
    private ErpOrderBusiness erpOrderBusiness;

    @Override
    public void childExec(TaskRecord taskRecord) {
        LogisticsOrderConfirmDTO confirmDTO = JSON.parseObject(taskRecord.getNotifyContent(), LogisticsOrderConfirmDTO.class);
        DeliveryConfirmDTO confirmData = BeanUtil.copyProperties(confirmDTO.getOrderConfirmData(), DeliveryConfirmDTO.class);
        Integer erpOrderType = YundaDeliveryOrderOrderTypeEnum.erpOrderTypeMap.getOrDefault(confirmData.getDeliveryOrder().getOrderType(), ErpRecordOrderTypeEnum.NORMAL.getValue());

        // 检查是否已存在
        ErpRecord record = erpRecordService.getOneByColumn(confirmData.getDeliveryOrder().getDeliveryOrderCode(), ErpRecord::getErpSn);
        if (ObjectUtil.isNotNull(record)) {
            log.error("【韵达ERP订单】流水号：{} 已存在", confirmData.getDeliveryOrder().getDeliveryOrderCode());
            String markdown = String.format("><font color=\"info\">ERP流水号</font>：%s\n", confirmData.getDeliveryOrder().getDeliveryOrderCode()) +
                    "><font color=\"warning\">异常原因</font>：已存在相同ERP流水号记录\n";
            erpOrderBusiness.erpOrderAlarm(markdown);
            return;
        }

        // 普通订单需要校验第三方订单号是否已存在
        if (erpOrderType.equals(ErpRecordOrderTypeEnum.NORMAL.getValue())) {
            LambdaQueryWrapper<ErpRecord> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(ErpRecord::getThirdOrderSn, confirmData.getDeliveryOrder().getOrderSourceCode())
                    .eq(ErpRecord::getErpOrderType, erpOrderType);
            ErpRecord checkThirdOrderSn = erpRecordService.getOneByWrapper(wrapper);
            if (ObjectUtil.isNotNull(checkThirdOrderSn)) {
                log.error("【韵达ERP订单】第三方订单号：{} 已存在", confirmData.getDeliveryOrder().getOrderSourceCode());
                String markdown = String.format("><font color=\"info\">ERP流水号</font>：%s\n", confirmData.getDeliveryOrder().getDeliveryOrderCode()) +
                        String.format("><font color=\"warning\">异常原因</font>：第三方流水号：%s 已存在相同记录\n", confirmData.getDeliveryOrder().getOrderSourceCode());
                erpOrderBusiness.erpOrderAlarm(markdown);
                return;
            }
        }

        // 记录erp订单信息
        ErpRecord erpRecord = new ErpRecord();
        erpRecord.setErpSn(confirmData.getDeliveryOrder().getDeliveryOrderCode());
        erpRecord.setErpOrderType(erpOrderType);
        erpRecord.setErpOrderSource(ErpRecordOrderSourceEnum.YUNDA.getValue());
        erpRecord.setOrderPlatform(confirmData.getDeliveryOrder().getSourcePlatformCode());
        erpRecord.setThirdOrderSn(confirmData.getDeliveryOrder().getOrderSourceCode());
        erpRecord.setPaidAmount(confirmData.getDeliveryOrder().getArAmount());
        erpRecord.setPaymentSn(confirmData.getDeliveryOrder().getPayNo());
        erpRecord.setPaidAt(LocalDateTime.parse(confirmData.getDeliveryOrder().getPayTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")));
        // 记录收货人信息
        String namePattern = "[\\u4e00-\\u9fa5\\w\\*]+";
        if (Pattern.matches(namePattern, confirmData.getDeliveryOrder().getReceiver().getReceiverName())) {
            erpRecord.setSendName(confirmData.getDeliveryOrder().getReceiver().getReceiverName());
        } else {
            erpRecord.setSendName("***");
        }
        String phonePattern = "^[0-9]*$";
        if (Pattern.matches(phonePattern, confirmData.getDeliveryOrder().getReceiver().getReceiverMobile())) {
            erpRecord.setSendPhone(confirmData.getDeliveryOrder().getReceiver().getReceiverMobile());
        } else {
            erpRecord.setSendPhone("");
        }
        String addressPattern = "[\\u4e00-\\u9fa5\\w\\-\\(\\)\\（\\）]+";
        if (Pattern.matches(addressPattern, confirmData.getDeliveryOrder().getReceiver().getReceiverDetailAddress())) {
            erpRecord.setSendAddress(confirmData.getDeliveryOrder().getReceiver().getReceiverDetailAddress());
        } else {
            erpRecord.setSendAddress("");
        }
        erpRecord.setSendArea(confirmData.getDeliveryOrder().getReceiver().getReceiverProvince() + " " +
                confirmData.getDeliveryOrder().getReceiver().getReceiverCity() + " " +
                confirmData.getDeliveryOrder().getReceiver().getReceiverArea());

        erpRecord.setExpressNumber(confirmData.getAPackage().get(0).getExpressCode());
        erpRecord.setExpressCorp(YundaLogiticsCodeEnum.map.getOrDefault(confirmData.getAPackage().get(0).getLogisticsCode(),
                confirmData.getAPackage().get(0).getLogisticsCode()));
        erpRecord.setDeliveryTime(ObjectUtils.isNotEmpty(confirmData.getDeliveryOrder().getOrderConfirmTime()) ?
                LocalDateTime.parse(confirmData.getDeliveryOrder().getOrderConfirmTime(), DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss")) :
                LocalDateTime.now());
        erpRecord.setStatus(ErpRecordStatusEnum.WAITING.getValue());
        erpRecord.setOperator(confirmData.getDeliveryOrder().getOperatorName());
        if (ObjectUtils.isNotEmpty(confirmDTO.getRemark())) {
            erpRecord.setDeliveryRemark(confirmDTO.getRemark());
        }

        // 记录原数据
        erpRecord.setRawData(confirmDTO.getRawData());

        // 产品包
        List<ErpLogisticsSkuBO> skuList = new ArrayList<>();
        confirmData.getOrderLine().forEach(orderLine -> {
            ErpLogisticsSkuBO skuBO = new ErpLogisticsSkuBO();
            if (orderLine.getItemCode().contains("CarPackage") || orderLine.getItemCode().contains("Truck")) {
                skuBO.setPackageSn(orderLine.getItemCode());
                skuBO.setPackageName(orderLine.getItemName());
            } else {
                skuBO.setSku(orderLine.getItemCode());
                skuBO.setSkuName(orderLine.getItemName());
            }
            skuBO.setCount(orderLine.getActualQty());
            skuList.add(skuBO);
        });
        // 记录发货商品信息
        erpRecord.setLogisticsSku(JSON.toJSONString(skuList));

        erpRecordService.create(erpRecord);

        TaskLogisticsConfirmYundaBO confirmYundaBO = new TaskLogisticsConfirmYundaBO();
        confirmYundaBO.setWarehouseNo(confirmData.getDeliveryOrder().getWarehouseCode());
        confirmYundaBO.setExpressCorpNo(confirmData.getAPackage().get(0).getLogisticsCode());
        confirmYundaBO.setExpressCorp(
                YundaLogiticsCodeEnum.map.getOrDefault(
                        confirmData.getAPackage().get(0).getLogisticsCode(),
                        confirmData.getAPackage().get(0).getLogisticsCode()
                )
        );
        confirmYundaBO.setExpressNumber(confirmData.getAPackage().get(0).getExpressCode());
        confirmYundaBO.setDeliveryStatus(confirmData.getDeliveryOrder().getStatus());
        confirmYundaBO.setDeliveryTime(confirmData.getDeliveryOrder().getOrderConfirmTime());

        String content = JSON.toJSONString(confirmYundaBO);

        // 查询task_record
        TaskRecord erpOrderRecord = taskRecordService.getOneByCondition(confirmData.getDeliveryOrder().getDeliveryOrderCode(),
                TaskRecordReferTypeEnum.TASK_ERP_ORDER_HANDLE.getType(),
                content
        );

        // 生成处理erp订单任务
        if (ObjectUtils.isEmpty(erpOrderRecord)) {
            TaskRecordDTO recordDTO = new TaskRecordDTO();
            recordDTO.setReferType(TaskRecordReferTypeEnum.TASK_ERP_ORDER_HANDLE.getType());
            recordDTO.setReferSn(erpRecord.getErpSn());
            recordDTO.setNotifyContent(content);

            TaskFactory.create(TaskRecordReferTypeEnum.TASK_ERP_ORDER_HANDLE).addAndPush(recordDTO);
        }
    }
}
