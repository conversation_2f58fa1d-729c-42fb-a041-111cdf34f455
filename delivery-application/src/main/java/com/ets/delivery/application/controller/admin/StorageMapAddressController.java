package com.ets.delivery.application.controller.admin;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.ets.common.JsonResult;
import com.ets.delivery.application.app.business.StorageMapAddressBusiness;
import com.ets.delivery.application.common.dto.storageMap.*;
import com.ets.delivery.application.common.vo.storageMap.StorageMapAddressProductPackageListVO;
import com.ets.delivery.application.common.vo.storageMap.*;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import jakarta.validation.Valid;

@RestController
@RequestMapping("/admin/storageMapAddress")
public class StorageMapAddressController {

    @Autowired
    private StorageMapAddressBusiness storageMapAddressBusiness;

    @PostMapping("/getList")
    public JsonResult<IPage<StorageMapAddressListVO>> getList(@RequestBody @Valid StorageMapAddressListDTO listDTO) {
        return JsonResult.ok(storageMapAddressBusiness.getList(listDTO));
    }

    @PostMapping("/getDetail")
    public JsonResult<StorageMapAddressDetailVO> getDetail(@RequestBody @Valid StorageMapAddressDetailDTO detailDTO) {
        return JsonResult.ok(storageMapAddressBusiness.getDetail(detailDTO));
    }

    @PostMapping("/create")
    public JsonResult<?> create(@RequestBody @Valid StorageMapAddressCreateDTO createDTO) {
        storageMapAddressBusiness.create(createDTO);
        return JsonResult.ok();
    }

    @PostMapping("/edit")
    public JsonResult<?> edit(@RequestBody @Valid StorageMapAddressEditDTO editDTO) {
        storageMapAddressBusiness.edit(editDTO);
        return JsonResult.ok();
    }

    @PostMapping("/delete")
    public JsonResult<?> delete(@RequestBody @Valid StorageMapAddressDeleteDTO deleteDTO) {
        storageMapAddressBusiness.delete(deleteDTO);
        return JsonResult.ok();
    }

    @PostMapping("/release")
    public JsonResult<?> release() {
        storageMapAddressBusiness.release();
        return JsonResult.ok();
    }

    @PostMapping("/setDefault")
    public JsonResult<?> setDefault(@RequestBody @Valid StorageMapAddressSetDefaultDTO setDefaultDTO) {
        storageMapAddressBusiness.setDefault(setDefaultDTO);
        return JsonResult.ok();
    }

    @PostMapping("/productPackageList")
    public JsonResult<IPage<StorageMapAddressProductPackageListVO>> productPackageList(@RequestBody @Valid StorageMapAddressProductPackageListDTO productPackageListDTO) {
        return JsonResult.ok(storageMapAddressBusiness.getProductPackageList(productPackageListDTO));
    }
}
