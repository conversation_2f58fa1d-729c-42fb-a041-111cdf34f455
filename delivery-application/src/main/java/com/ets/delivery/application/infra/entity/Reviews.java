package com.ets.delivery.application.infra.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.time.LocalDateTime;

/**
 * <p>
 * 审核单列表
 * </p>
 *
 * <AUTHOR>
 * @since 2021-09-02
 */
@Data
@EqualsAndHashCode(callSuper = true)
@TableName("etc_reviews")
public class Reviews extends BaseEntity<Reviews> {

    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 审核单流水号
     */
    private String reviewSn;

    /**
     * 订单流水号
     */
    private String orderSn;

    /**
     * 原始订单号（冗余）
     */
    private String originOrderSn;

    /**
     * 来源方
     */
    private String appId;

    /**
     * 对接方id
     */
    private Integer issuerId;

    /**
     * 发卡方名称
     */
    private String issuerName;

    /**
     * 订单来源
     */
    private String orderSource;

    /**
     * 订单类型
     */
    private String orderType;

    /**
     * 渠道
     */
    private String channel;

    /**
     * 是否货车，0否1是
     */
    private Integer isTruck;

    /**
     * 车辆归属：1个人2单位车
     */
    private Integer vehicleBelong;

    /**
     * 车牌号
     */
    private String plateNo;

    /**
     * 车牌颜色
     */
    private String plateColor;

    /**
     * 具体审核内容，申办订单包含用户信息，车辆信息，营业执照信息等
     */
    private String contentParams;

    /**
     * 所属仓储
     */
    private String storageCode;

    /**
     * 处理人
     */
    private String operator;

    /**
     * 资料审核状态,0待审核，1审核中，2审核通过，3审核拒绝，4审核取消
     */
    private Integer reviewStatus;

    /**
     * 资料审核驳回状态编码
     */
    private String reviewCode;

    /**
     * 资料审核备注
     */
    private String reviewRemark;

    /**
     * 资料审核时间
     */
    private LocalDateTime reviewTime;

    /**
     * 领取时间
     */
    private LocalDateTime drawTime;

    /**
     * 状态：1正常2取消3暂停
     */
    private Integer status;

    /**
     * 推送发卡方状态,0待推送，1推送中，2推送成功，3推送失败，4推送取消，5推送结果未定义
     */
    private Integer pushIssuerStatus;

    /**
     * 推送备注
     */
    private String pushIssuerRemark;

    /**
     * 推送时间
     */
    private LocalDateTime pushIssuerTime;

    /**
     * 通知申办的状态：0待通知，1通知成功，2通知失败
     */
    private Integer notifyStatus;

    /**
     * 通知成功时间
     */
    private LocalDateTime notifyTime;

    /**
     * 身份证审核状态[0-未审 1-审核中 2-初审通过 3-初审不通过 4-复审中 5-复审通过 6-复审不通过 9-无需审核]
     */
    private Integer idcardsStatus;

    /**
     * 行驶证审核状态[0-未审 1-审核中 2-初审通过 3-初审不通过 4-复审中 5-复审通过 6-复审不通过 9-无需审核]
     */
    private Integer vehiclesStatus;

    /**
     * 车身照审核状态[0-未审 1-审核中 2-初审通过 3-初审不通过 4-复审中 5-复审通过 6-复审不通过 9-无需审核]
     */
    private Integer vehiclesBodyStatus;

    /**
     * 道路运输证审核状态[0-未审 1-审核中 2-初审通过 3-初审不通过 4-复审中 5-复审通过 6-复审不通过 9-无需审核]
     */
    private Integer licencesRoadStatus;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;

    /**
     * 是否推送自动审核：0、人工审核 1、自动审核
     */
    private Integer autoAudit;

    /**
     * 后审类型:默认0-无需后审 1-正常后审 2-优先后审 3-紧急后审
     */
    private Integer emergencyType;
}
