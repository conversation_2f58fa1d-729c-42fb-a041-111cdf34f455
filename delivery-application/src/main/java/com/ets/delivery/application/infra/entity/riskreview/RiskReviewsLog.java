package com.ets.delivery.application.infra.entity.riskreview;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ets.delivery.application.infra.entity.BaseEntity;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * <p>
 * 风控审核单日志
 * </p>
 *
 * <AUTHOR>
 * @since 2025-06-23
 */
@Data
@TableName("etc_risk_reviews_log")
public class RiskReviewsLog extends BaseEntity<RiskReviewsLog> {

    private static final long serialVersionUID = 1L;

    /**
     * 自增id
     */
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 风控审核单号
     */
    private String riskOrderSn;

    /**
     * 操作内容
     */
    private String operateContent;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 操作类型
     */
    private String type;

    /**
     * 创建时间
     */
    private LocalDateTime createdAt;

    /**
     * 更新时间
     */
    private LocalDateTime updatedAt;
}
