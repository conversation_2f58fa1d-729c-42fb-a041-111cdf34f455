package com.ets.delivery.application.common.dto.riskreview;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 取消风险审核订单请求参数
 */
@Data
public class RiskReviewOrderCancelDTO {
    
    /**
     * 风险审核单流水号
     */
    @NotBlank(message = "风险审核单流水号不能为空")
    private String reviewSn;

    /**
     * 取消原因
     */
    @NotBlank(message = "取消原因不能为空")
    private String cancelReason;
}
