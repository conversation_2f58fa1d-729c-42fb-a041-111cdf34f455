package com.ets.delivery.application.app.business;

import cn.hutool.core.bean.BeanUtil;
import com.ets.common.ToolsHelper;
import com.ets.delivery.application.common.consts.riskreview.RiskReviewStatusEnum;
import com.ets.delivery.application.common.dto.riskreview.RiskReviewOrderCreateDTO;
import com.ets.delivery.application.infra.entity.riskreview.RiskReviewsOrder;
import com.ets.delivery.application.infra.entity.riskreview.RiskReviewsOrderInfo;
import com.ets.delivery.application.infra.entity.riskreview.RiskReviewsLog;
import com.ets.delivery.application.infra.entity.Reviews;
import com.ets.delivery.application.infra.service.RiskReviewsOrderService;
import com.ets.delivery.application.infra.service.RiskReviewsOrderInfoService;
import com.ets.delivery.application.infra.service.RiskReviewsLogService;
import com.ets.delivery.application.infra.service.ReviewsService;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import java.util.Arrays;

@Slf4j
@Component
public class RiskReviewOrderBusiness {

    @Autowired
    private RiskReviewsOrderService riskReviewsOrderService;

    @Autowired
    private RiskReviewsOrderInfoService riskReviewsOrderInfoService;

    @Autowired
    private RiskReviewsLogService riskReviewsLogService;

    @Autowired
    private ReviewsService reviewsService;

    @Resource(name = "redisPermanentTemplate")
    private StringRedisTemplate redisPermanentTemplate;

    @Value("${app.env}")
    private String active;

    /**
     * 创建风控审核单
     *
     * @param dto 创建风控审核单请求参数
     * @return 风控单号
     */
    public String createRiskReviewOrder(RiskReviewOrderCreateDTO dto) {
        // 加锁，防止并发操作
        String lockKey = "riskReviewOrder:" + dto.getBusinessSn();
        if (!ToolsHelper.addLock(redisPermanentTemplate, lockKey, 30)) {
            ToolsHelper.throwException("风控审核单创建中，请稍后再试");
        }

        try {
            // 检查业务单是否存在待审核记录
            RiskReviewsOrder existingOrder = riskReviewsOrderService.getByBusinessSn(dto.getBusinessSn());
            if (existingOrder != null
                    && Arrays.asList(RiskReviewStatusEnum.PENDING.getValue(), RiskReviewStatusEnum.CANCELED.getValue()
            ).contains(existingOrder.getRiskReviewStatus())) {
                ToolsHelper.throwException("该业务单已存在待审核记录，请勿重复提交");
            }

            // TODO: 查询风控记录，验证风控记录不通过才能生成
            // 这里需要根据riskSn查询风控记录状态，确认不通过才能创建审核单

            // 创建风控审核单
            RiskReviewsOrder riskReviewsOrder = BeanUtil.copyProperties(dto, RiskReviewsOrder.class);

            // 生成风控单号
            String riskOrderSn = ToolsHelper.genNum(redisPermanentTemplate, "RiskReviewOrder", active, 8);
            riskReviewsOrder.setRiskOrderSn(riskOrderSn);
            riskReviewsOrder.setRiskReviewStatus(RiskReviewStatusEnum.PENDING.getValue()); // 待审核

            // 保存风控审核单
            riskReviewsOrderService.create(riskReviewsOrder);

            // 查询申办审核单并记录到审核资料表
            Reviews reviews = reviewsService.getOneNeedRiskByOrderSn(dto.getBusinessSn());
            if (reviews != null) {
                RiskReviewsOrderInfo orderInfo = new RiskReviewsOrderInfo();
                orderInfo.setRiskOrderSn(riskOrderSn);
                orderInfo.setReviewSn(reviews.getReviewSn());
                riskReviewsOrderInfoService.create(orderInfo);

                log.info("风控审核单关联申办审核单成功，风控单号：{}，申办审核单号：{}", riskOrderSn, reviews.getReviewSn());
            } else {
                log.warn("未找到对应的申办审核单，风控单号：{}，业务单号：{}", riskOrderSn, dto.getBusinessSn());
            }

            // 记录操作日志
            RiskReviewsLog reviewsLog = new RiskReviewsLog();
            reviewsLog.setRiskOrderSn(riskOrderSn);
            reviewsLog.setOperateContent("创建风控审核单");
            reviewsLog.setOperator("system");
            reviewsLog.setType("create");
            riskReviewsLogService.create(reviewsLog);

            log.info("创建风控审核单成功，风控单号：{}，业务单号：{}，风控流水号：{}",
                    riskOrderSn, dto.getBusinessSn(), dto.getRiskSn());

            return riskOrderSn;
        } finally {
            // 释放锁
            ToolsHelper.unLock(redisPermanentTemplate, lockKey);
        }
    }
}