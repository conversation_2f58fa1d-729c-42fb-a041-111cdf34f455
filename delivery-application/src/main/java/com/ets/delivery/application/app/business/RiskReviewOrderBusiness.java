package com.ets.delivery.application.app.business;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.util.StrUtil;
import com.ets.delivery.application.common.consts.riskreview.RiskReviewStatusEnum;
import com.ets.delivery.application.common.dto.riskreview.RiskReviewOrderCreateDTO;
import com.ets.delivery.application.infra.entity.riskreview.RiskReviewsOrder;
import com.ets.delivery.application.infra.entity.riskreview.RiskReviewsOrderInfo;
import com.ets.delivery.application.infra.entity.riskreview.RiskReviewsLog;
import com.ets.delivery.application.infra.service.RiskReviewsOrderService;
import com.ets.delivery.application.infra.service.RiskReviewsOrderInfoService;
import com.ets.delivery.application.infra.service.RiskReviewsLogService;
import com.ets.tools.ToolsHelper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;

@Slf4j
@Component
public class RiskReviewOrderBusiness {

    @Autowired
    private RiskReviewsOrderService riskReviewsOrderService;

    @Autowired
    private RiskReviewsOrderInfoService riskReviewsOrderInfoService;

    @Autowired
    private RiskReviewsLogService riskReviewsLogService;

    @Resource(name = "redisPermanentTemplate")
    private StringRedisTemplate redisPermanentTemplate;

    @Value("${app.env}")
    private String active;
