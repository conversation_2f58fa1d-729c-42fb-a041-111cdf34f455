package com.ets.delivery.application.controller.nologin;

import com.ets.common.JsonResult;
import com.ets.delivery.application.app.business.RiskReviewOrderBusiness;
import com.ets.delivery.application.common.dto.riskreview.RiskInfoUploadDTO;
import com.ets.delivery.application.common.dto.riskreview.RiskReviewOrderCancelDTO;
import com.ets.delivery.application.common.dto.riskreview.RiskReviewOrderCreateDTO;
import jakarta.validation.Valid;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 风险审核订单
 */
@RestController
@RequestMapping("/risk-review-order")
public class RiskReviewOrderController {

    @Autowired
    private RiskReviewOrderBusiness riskReviewOrderBusiness;

    /**
     * 创建风险审核订单
     *
     * @param createDTO 创建风险审核订单请求参数
     * @return 创建结果
     */
    @PostMapping("/create")
    public JsonResult<Void> create(@RequestBody @Valid RiskReviewOrderCreateDTO createDTO) {
        return JsonResult.ok();
    }

    /**
     * 取消风险审核订单
     *
     * @param cancelDTO 取消风险审核订单请求参数
     * @return 取消结果
     */
    @PostMapping("/cancel")
    public JsonResult<Void> cancel(@RequestBody @Valid RiskReviewOrderCancelDTO cancelDTO) {
        return JsonResult.ok();
    }

    /**
     * 上传风险信息
     *
     * @param infoUploadDTO 风险信息上传请求参数
     * @return 上传结果
     */
    @PostMapping("/risk-info-upload")
    public JsonResult<Void> riskInfoUpload(@RequestBody @Valid RiskInfoUploadDTO infoUploadDTO) {
        return JsonResult.ok();
    }

}
