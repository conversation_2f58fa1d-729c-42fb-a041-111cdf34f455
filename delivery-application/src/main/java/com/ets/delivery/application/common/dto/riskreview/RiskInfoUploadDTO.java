package com.ets.delivery.application.common.dto.riskreview;

import jakarta.validation.constraints.NotBlank;
import lombok.Data;

/**
 * 风险信息上传请求参数
 */
@Data
public class RiskInfoUploadDTO {

    /**
     * 风控单号
     */
    @NotBlank(message = "风控单号不能为空")
    private String riskOrderSn;

    /**
     * 业务审核单号
     */
    private String reviewSn;

    /**
     * 补充图片url
     */
    @NotBlank(message = "补充图片url不能为空")
    private String additionalImgUrl;
}
