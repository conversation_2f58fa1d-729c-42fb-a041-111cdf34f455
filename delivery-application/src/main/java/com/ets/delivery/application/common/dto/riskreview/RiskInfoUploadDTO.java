package com.ets.delivery.application.common.dto.riskreview;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

/**
 * 风险信息上传请求参数
 */
@Data
public class RiskInfoUploadDTO {

    /**
     * 风险审核单流水号
     */
    @NotBlank(message = "风险审核单流水号不能为空")
    private String reviewSn;

    /**
     * 上传类型 1-补充证据 2-风险报告 3-处理结果
     */
    @NotNull(message = "上传类型不能为空")
    private Integer uploadType;

    /**
     * 文件信息
     */
    @NotNull(message = "文件信息不能为空")
    private FileInfoDTO fileInfo;

    /**
     * 上传说明
     */
    private String uploadDescription;

    /**
     * 文件信息数据类型
     */
    @Data
    public static class FileInfoDTO {
        /**
         * 文件名称
         */
        @NotBlank(message = "文件名称不能为空")
        private String fileName;

        /**
         * 文件URL
         */
        @NotBlank(message = "文件URL不能为空")
        private String fileUrl;

        /**
         * 文件类型
         */
        @NotBlank(message = "文件类型不能为空")
        private String fileType;

        /**
         * 文件大小（字节）
         */
        private Long fileSize;

        /**
         * 文件MD5
         */
        private String fileMd5;
    }
}
